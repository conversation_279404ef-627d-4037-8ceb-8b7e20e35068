# Easy Agent Center Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# LLM Configuration
# =============================================================================

# OpenAI API Key (required for OpenAI agents)
OPENAI_API_KEY=your-openai-api-key-here

# Default LLM Configuration (optional)
# These will be used if no specific LLM is provided when creating agents
DEFAULT_API_BASE=https://ark.cn-beijing.volces.com/api/v3/
DEFAULT_API_KEY=your-default-api-key-here
DEFAULT_MODEL=deepseek-v3-250324

# =============================================================================
# MySQL Database Configuration
# =============================================================================

# Database connection settings
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-mysql-password
DB_DATABASE=easy_agent_center
ALEMBIC_DB_USERNAME=root
ALEMBIC_DB_PASSWORD=your-mysql-password

# Database connection pool settings
DB_CHARSET=utf8mb4
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Debug settings
DB_ECHO=true

# =============================================================================
# Redis Configuration
# =============================================================================

# Redis connection settings
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# =============================================================================
# MCP Configuration
# =============================================================================

# MCP synchronization interval in seconds (default: 60)
MCP_SYNC_INTERVAL=60

# =============================================================================
# Third-Party Authentication Configuration
# =============================================================================

# Third-party API configuration
THIRD_PARTY_AUTH_BASE_URL=https://live-api.yanxiu.com
THIRD_PARTY_API_URL=https://x-api.3ren.cn/user-hub/oa/login
THIRD_PARTY_APP_KEY=wand-3ren
THIRD_PARTY_PASSPORT=your-third-party-username
THIRD_PARTY_PASSWORD=your-third-party-password

# =============================================================================
# Application Configuration
# =============================================================================

# Server settings
HOST=0.0.0.0
PORT=8000

# CORS settings (comma-separated origins, or "*" for all)
CORS_ORIGINS=*

# Logging configuration
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_DIR=logs

# =============================================================================
# Development/Testing Configuration
# =============================================================================

# Environment (development, production, testing)
ENVIRONMENT=development

# Test database (used for running tests)
TEST_DB_DATABASE=test_easy_agent_center

# =============================================================================
# TOS Storage Configuration
# =============================================================================

# TOS access credentials
TOS_ACCESS_KEY=your-tos-access-key
TOS_SECRET_KEY=your-tos-secret-key

# TOS service configuration
TOS_ENDPOINT=your-tos-endpoint
TOS_REGION=your-tos-region
TOS_BUCKET_NAME=your-tos-bucket-name
TOS_DOMAIN=srtupload-dev.jsyxw.cn

# =============================================================================
# Volcengine Configuration
# =============================================================================

# Volcengine access credentials
VOLCENGINE_ACCESS_KEY=your-volcengine-access-key
VOLCENGINE_SECRET_KEY=your-volcengine-secret-key
VOLCENGINE_REGION=cn-beijing

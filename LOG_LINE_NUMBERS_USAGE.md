# 日志器行号功能使用示例

## 基本使用

```python
from app.logger.logger import AgentLogger

# 创建带行号显示的日志器（默认）
logger = AgentLogger("my_service")
logger.info("这将显示行号")  # 输出: [filename:line] 这将显示行号

# 创建不显示行号的日志器（高性能场景）
fast_logger = AgentLogger("performance_critical", show_line_numbers=False)
fast_logger.info("这不会显示行号")  # 输出: 这不会显示行号
```

## 现有代码兼容性

所有现有的 95 个 `self.logger.error/info` 调用都将自动获得行号显示功能，无需修改任何现有代码。

## 性能考虑

- 启用行号：每次日志调用增加约 10-50μs 开销
- 禁用行号：几乎没有性能开销
- 建议：在生产环境中根据需要选择是否启用行号功能

## 功能特性

- ✅ 自动显示调用者的文件名和行号
- ✅ 跨平台兼容（Windows/Linux/macOS）
- ✅ 内存安全（防止栈帧泄露）
- ✅ 配置灵活（可开启/关闭）
- ✅ 向后兼容（现有代码无需修改）
"""
Pydantic models for Smart Configuration API endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from app.schemas.common import PaginatedResponse


class SmartConfigCreate(BaseModel):
    """Request model for creating a new smart configuration."""
    config_key: str = Field(..., min_length=1, max_length=64, description="Primary configuration key")
    config_value: str = Field(..., description="Configuration value")
    second_key: str = Field("", max_length=64, description="Secondary configuration key")
    config_order: int = Field(0, ge=0, le=99, description="Order for sorting")
    ext_info: Optional[str] = Field(None, max_length=255, description="Extended information")
    config_type: int = Field(0, ge=0, le=1, description="Configuration type: 0=single, 1=chunk")
    created_by: int = Field(0, ge=0, description="Creator user ID")


class SmartConfigUpdate(BaseModel):
    """Request model for updating a smart configuration."""
    config_value: Optional[str] = Field(None, description="Configuration value")
    config_order: Optional[int] = Field(None, ge=0, le=99, description="Order for sorting")
    ext_info: Optional[str] = Field(None, max_length=255, description="Extended information")
    config_type: Optional[int] = Field(None, ge=0, le=1, description="Configuration type: 0=single, 1=chunk")
    updated_by: int = Field(0, ge=0, description="Updater user ID")


class SmartConfigResponse(BaseModel):
    """Response model for smart configuration."""
    id: int = Field(..., description="Configuration ID")
    config_key: str = Field(..., description="Primary configuration key")
    second_key: str = Field(..., description="Secondary configuration key")
    config_value: str = Field(..., description="Configuration value")
    config_order: int = Field(..., description="Order for sorting")
    ext_info: Optional[str] = Field(None, description="Extended information")
    config_type: int = Field(..., description="Configuration type: 0=single, 1=chunk")
    invalid: int = Field(..., description="Delete flag: 0=valid, 1=deleted")
    created_by: int = Field(..., description="Creator user ID")
    updated_by: int = Field(..., description="Updater user ID")
    gmt_create: Optional[datetime] = Field(None, description="Creation time")
    gmt_modified: Optional[datetime] = Field(None, description="Modification time")
    ts: Optional[datetime] = Field(None, description="Timestamp")

    class Config:
        from_attributes = True


class SmartConfigListResponse(PaginatedResponse[SmartConfigResponse]):
    """Paginated response model for smart configurations list."""
    pass


class SmartConfigDeleteResponse(BaseModel):
    """Response model for configuration deletion."""
    message: str = Field(..., description="Success message")
    deleted: bool = Field(..., description="Whether deletion was successful")


class ConfigQueryParams(BaseModel):
    """Query parameters for configuration listing."""
    config_key: Optional[str] = Field(None, description="Filter by config key")
    second_key: Optional[str] = Field(None, description="Filter by second key")
    config_type: Optional[int] = Field(None, ge=0, le=1, description="Filter by config type")
    offset: int = Field(0, ge=0, description="Offset for pagination")
    limit: int = Field(100, ge=1, le=1000, description="Limit for pagination")
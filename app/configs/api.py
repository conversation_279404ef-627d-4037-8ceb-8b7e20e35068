"""
Smart Configuration management API endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional

from app.logger.logger import get_logger, PerformanceLogger
from app.database.smart_config_service import SmartConfigService
from app.auth.security import get_current_internal_user
from app.configs.schemas import (
    SmartConfigCreate,
    SmartConfigUpdate,
    SmartConfigResponse,
    SmartConfigListResponse,
    SmartConfigDeleteResponse
)

# Initialize router and logger
router = APIRouter(prefix="/configs", tags=["智能配置管理"], dependencies=[Depends(get_current_internal_user)])
api_logger = get_logger("smart_config.api")


@router.get("", response_model=SmartConfigListResponse, summary="列出智能配置")
async def list_configs(
    config_key: Optional[str] = Query(None, description="配置键过滤"),
    second_key: Optional[str] = Query(None, description="二级键过滤"),
    config_type: Optional[int] = Query(None, ge=0, le=1, description="配置类型过滤"),
    offset: int = Query(0, ge=0, description="分页偏移"),
    limit: int = Query(100, ge=1, le=1000, description="分页限制")
):
    """列出智能配置，支持过滤和分页。"""
    try:
        with PerformanceLogger("api_list_configs", api_logger):
            configs, total = await SmartConfigService.list_configs(
                config_key=config_key,
                second_key=second_key,
                config_type=config_type,
                offset=offset,
                limit=limit
            )
            
            config_responses = [SmartConfigResponse(**config.to_dict()) for config in configs]
            
            # Calculate page and total_pages
            page = (offset // limit) + 1
            total_pages = (total + limit - 1) // limit
            
            return SmartConfigListResponse(
                data=config_responses,
                total=total,
                page=page,
                size=limit,
                total_pages=total_pages
            )
    except Exception as e:
        api_logger.error("列出智能配置失败", exception=e)
        raise HTTPException(status_code=500, detail=f"列出智能配置时出错: {str(e)}")


@router.post("", response_model=SmartConfigResponse, summary="创建智能配置")
async def create_config(request: SmartConfigCreate):
    """创建新的智能配置。"""
    try:
        with PerformanceLogger(f"api_create_config_{request.config_key}", api_logger):
            # Check if config already exists
            existing_config = await SmartConfigService.get_config_by_keys(
                request.config_key, 
                request.second_key
            )
            if existing_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"配置 '{request.config_key}:{request.second_key}' 已存在"
                )
            
            config = await SmartConfigService.create_config(
                config_key=request.config_key,
                config_value=request.config_value,
                second_key=request.second_key,
                config_order=request.config_order,
                ext_info=request.ext_info,
                config_type=request.config_type,
                created_by=request.created_by
            )
            
            api_logger.info(f"✅ 智能配置创建成功: {request.config_key}:{request.second_key}")
            return SmartConfigResponse(**config.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"创建智能配置失败: {request.config_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"创建智能配置时出错: {str(e)}")


@router.get("/{config_key}", response_model=SmartConfigResponse, summary="获取智能配置")
async def get_config(config_key: str, second_key: Optional[str] = Query(None, description="二级键")):
    """获取指定键的智能配置。"""
    try:
        second_key = second_key or ""
        config = await SmartConfigService.get_config_by_keys(config_key, second_key)
        
        if not config:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到配置: {config_key}:{second_key}"
            )
        
        return SmartConfigResponse(**config.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取智能配置时出错: {str(e)}")


@router.get("/{config_key}/{second_key}", response_model=SmartConfigResponse, summary="获取智能配置（完整路径）")
async def get_config_full_path(config_key: str, second_key: str):
    """通过完整路径获取智能配置。"""
    try:
        config = await SmartConfigService.get_config_by_keys(config_key, second_key)
        
        if not config:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到配置: {config_key}:{second_key}"
            )
        
        return SmartConfigResponse(**config.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取智能配置时出错: {str(e)}")


@router.put("/{config_key}", response_model=SmartConfigResponse, summary="更新智能配置")
async def update_config(
    config_key: str, 
    request: SmartConfigUpdate,
    second_key: Optional[str] = Query(None, description="二级键")
):
    """更新智能配置。"""
    try:
        second_key = second_key or ""
        
        with PerformanceLogger(f"api_update_config_{config_key}", api_logger):
            # Convert request to dict, removing None values
            updates = request.model_dump(exclude_none=True)
            
            config = await SmartConfigService.update_config(
                config_key=config_key,
                second_key=second_key,
                **updates
            )
            
            if not config:
                raise HTTPException(
                    status_code=404, 
                    detail=f"未找到配置: {config_key}:{second_key}"
                )
            
            api_logger.info(f"✅ 智能配置更新成功: {config_key}:{second_key}")
            return SmartConfigResponse(**config.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"更新智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新智能配置时出错: {str(e)}")


@router.put("/{config_key}/{second_key}", response_model=SmartConfigResponse, summary="更新智能配置（完整路径）")
async def update_config_full_path(config_key: str, second_key: str, request: SmartConfigUpdate):
    """通过完整路径更新智能配置。"""
    try:
        with PerformanceLogger(f"api_update_config_{config_key}", api_logger):
            # Convert request to dict, removing None values
            updates = request.model_dump(exclude_none=True)
            
            config = await SmartConfigService.update_config(
                config_key=config_key,
                second_key=second_key,
                **updates
            )
            
            if not config:
                raise HTTPException(
                    status_code=404, 
                    detail=f"未找到配置: {config_key}:{second_key}"
                )
            
            api_logger.info(f"✅ 智能配置更新成功: {config_key}:{second_key}")
            return SmartConfigResponse(**config.to_dict())
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"更新智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新智能配置时出错: {str(e)}")


@router.delete("/{config_key}", response_model=SmartConfigDeleteResponse, summary="删除智能配置")
async def delete_config(
    config_key: str, 
    second_key: Optional[str] = Query(None, description="二级键")
):
    """软删除智能配置。"""
    try:
        second_key = second_key or ""
        
        with PerformanceLogger(f"api_delete_config_{config_key}", api_logger):
            deleted = await SmartConfigService.delete_config(config_key, second_key)
            
            if not deleted:
                raise HTTPException(
                    status_code=404, 
                    detail=f"未找到配置: {config_key}:{second_key}"
                )
            
            api_logger.info(f"✅ 智能配置删除成功: {config_key}:{second_key}")
            return SmartConfigDeleteResponse(
                message=f"配置 '{config_key}:{second_key}' 删除成功",
                deleted=True
            )
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"删除智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除智能配置时出错: {str(e)}")


@router.delete("/{config_key}/{second_key}", response_model=SmartConfigDeleteResponse, summary="删除智能配置（完整路径）")
async def delete_config_full_path(config_key: str, second_key: str):
    """通过完整路径删除智能配置。"""
    try:
        with PerformanceLogger(f"api_delete_config_{config_key}", api_logger):
            deleted = await SmartConfigService.delete_config(config_key, second_key)
            
            if not deleted:
                raise HTTPException(
                    status_code=404, 
                    detail=f"未找到配置: {config_key}:{second_key}"
                )
            
            api_logger.info(f"✅ 智能配置删除成功: {config_key}:{second_key}")
            return SmartConfigDeleteResponse(
                message=f"配置 '{config_key}:{second_key}' 删除成功",
                deleted=True
            )
    
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"删除智能配置失败: {config_key}:{second_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除智能配置时出错: {str(e)}")


@router.get("/{config_key}/group", response_model=List[SmartConfigResponse], summary="获取配置组")
async def get_config_group(config_key: str):
    """获取指定配置键下的所有配置。"""
    try:
        configs = await SmartConfigService.get_configs_by_key(config_key)
        config_responses = [SmartConfigResponse(**config.to_dict()) for config in configs]
        
        return config_responses
    
    except Exception as e:
        api_logger.error(f"获取配置组失败: {config_key}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取配置组时出错: {str(e)}")
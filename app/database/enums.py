"""
Upload status enum for Easy Agent Center.
"""

from enum import Enum as PyEnum


class UploadStatus(PyEnum):
    """文件上传状态枚举 - 业务逻辑使用"""
    
    PENDING = "pending"          # 待处理
    EXTRACTING = "extracting"    # 提取中
    SUCCESS = "success"          # 成功
    FAILED = "failed"            # 失败
    
    def __str__(self):
        return self.value
    
    @classmethod
    def from_string(cls, value: str):
        """从字符串创建枚举实例"""
        for status in cls:
            if status.value == value:
                return status
        raise ValueError(f"Invalid upload status: {value}")
    
    @classmethod
    def get_valid_statuses(cls):
        """获取所有有效状态"""
        return [status.value for status in cls]
    
    @property
    def is_completed(self):
        """检查状态是否已完成（成功或失败）"""
        return self in [UploadStatus.SUCCESS, UploadStatus.FAILED]
    
    @property
    def is_processing(self):
        """检查状态是否正在处理中"""
        return self in [UploadStatus.EXTRACTING]
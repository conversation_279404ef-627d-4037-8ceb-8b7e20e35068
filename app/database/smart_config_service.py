"""
Smart Configuration Service for Easy Agent Center.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncSession

from .models import SmartConfig
from .connection import get_database_manager
from app.logger.logger import get_logger

logger = get_logger("smart_config.service")


class SmartConfigService:
    """Service for managing smart configurations in the database."""
    
    @staticmethod
    async def create_config(
        config_key: str,
        config_value: str,
        second_key: str = "",
        config_order: int = 0,
        ext_info: Optional[str] = None,
        config_type: int = 0,
        created_by: int = 0,
        session: Optional[AsyncSession] = None
    ) -> SmartConfig:
        """Create a new smart configuration."""
        
        async def _create(session: AsyncSession) -> SmartConfig:
            # Create new config
            config = SmartConfig(
                config_key=config_key,
                second_key=second_key,
                config_value=config_value,
                config_order=config_order,
                ext_info=ext_info,
                config_type=config_type,
                created_by=created_by,
                updated_by=created_by
            )
            
            session.add(config)
            await session.flush()
            await session.refresh(config)
            
            logger.info(f"Created smart config: {config_key}:{second_key}")
            return config
        
        if session:
            return await _create(session)
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as new_session:
                config = await _create(new_session)
                await new_session.commit()
                return config
    
    @staticmethod
    async def get_config_by_keys(
        config_key: str, 
        second_key: str = "",
        session: Optional[AsyncSession] = None
    ) -> Optional[SmartConfig]:
        """Get configuration by key combination."""
        if session:
            result = await session.execute(
                select(SmartConfig).where(
                    SmartConfig.config_key == config_key,
                    SmartConfig.second_key == second_key,
                    SmartConfig.invalid == 0
                )
            )
            return result.scalar_one_or_none()
        
        db_manager = get_database_manager()
        async with db_manager.get_session() as new_session:
            result = await new_session.execute(
                select(SmartConfig).where(
                    SmartConfig.config_key == config_key,
                    SmartConfig.second_key == second_key,
                    SmartConfig.invalid == 0
                )
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def list_configs(
        config_key: Optional[str] = None,
        second_key: Optional[str] = None,
        config_type: Optional[int] = None,
        offset: int = 0,
        limit: int = 100,
        session: Optional[AsyncSession] = None
    ) -> tuple[List[SmartConfig], int]:
        """List configurations with filtering and pagination."""
        
        async def _list(session: AsyncSession) -> tuple[List[SmartConfig], int]:
            # Build base query
            base_query = select(SmartConfig).where(SmartConfig.invalid == 0)
            
            if config_key:
                base_query = base_query.where(SmartConfig.config_key == config_key)
            if second_key:
                base_query = base_query.where(SmartConfig.second_key == second_key)
            if config_type is not None:
                base_query = base_query.where(SmartConfig.config_type == config_type)
            
            # Count total records
            count_query = select(func.count()).select_from(base_query.subquery())
            total_result = await session.execute(count_query)
            total_count = total_result.scalar_one()
            
            # Apply pagination
            query = base_query.order_by(
                SmartConfig.config_key.asc(),
                SmartConfig.second_key.asc(),
                SmartConfig.config_order.asc()
            ).offset(offset).limit(limit)
            
            result = await session.execute(query)
            configs = list(result.scalars().all())
            
            return configs, total_count
        
        if session:
            return await _list(session)
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as new_session:
                return await _list(new_session)
    
    @staticmethod
    async def update_config(
        config_key: str,
        second_key: str = "",
        **updates
    ) -> Optional[SmartConfig]:
        """Update a configuration."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            config = await SmartConfigService.get_config_by_keys(config_key, second_key, session)
            if not config:
                return None
            
            # Update fields
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            await session.commit()
            await session.refresh(config)
            logger.info(f"Updated smart config: {config_key}:{second_key}")
            
            return config
    
    @staticmethod
    async def delete_config(config_key: str, second_key: str = "") -> bool:
        """Soft delete a configuration."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                update(SmartConfig)
                .where(
                    SmartConfig.config_key == config_key,
                    SmartConfig.second_key == second_key,
                    SmartConfig.invalid == 0
                )
                .values(invalid=1)
            )
            
            if result.rowcount > 0:
                await session.commit()
                logger.info(f"Deleted smart config: {config_key}:{second_key}")
                return True
            
            return False
    
    @staticmethod
    async def get_configs_by_key(config_key: str) -> List[SmartConfig]:
        """Get all configurations for a specific config_key."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(SmartConfig)
                .where(
                    SmartConfig.config_key == config_key,
                    SmartConfig.invalid == 0
                )
                .order_by(SmartConfig.config_order.asc())
            )
            return list(result.scalars().all())
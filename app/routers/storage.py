"""
存储相关的 FastAPI 路由。

该模块提供文件上传、下载、删除等存储操作的 API 端点。
"""

import io
from urllib.parse import quote
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from fastapi.responses import StreamingResponse

from app.logger.logger import get_logger
from app.auth.security import get_current_user
from app.auth import schemas as auth_schemas
from app.clients.models import FileUploadResponse
from app.dependencies import get_file_upload_service
from app.schemas.common import PaginatedResponse
from app.database.enums import UploadStatus
# 从storage_service导入全局存储业务服务实例
from app.services.storage_service import storage_business_service
from app.services.file_extractor import file_extractor

def convert_record_to_response(record) -> FileUploadResponse:
    """Convert FileUploadRecord to FileUploadResponse."""
    return FileUploadResponse(
        id=record.id,
        original_filename=record.original_filename,
        file_size=record.file_size,
        content_type=record.content_type,
        storage_type=record.storage_type,
        object_key=record.object_key,
        file_url=record.file_url,
        upload_status=record.upload_status,
        error_message=record.error_message,
        uploader_name=record.uploader_name,
        file_extension=record.file_extension,
        readable_size=record.readable_size,
        created_at=record.created_at,
        updated_at=record.updated_at
    )

router = APIRouter(prefix="/storage", tags=["文件存储"], dependencies=[Depends(get_current_user)])
storage_logger = get_logger("storage.api")


@router.post("/upload", response_model=FileUploadResponse, summary="上传文件")
async def upload_file(
    file: UploadFile = File(...),
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    上传文件到存储服务
    
    Args:
        file: 要上传的文件
        current_user: 当前用户信息
        
    Returns:
        FileUploadResponse: 上传响应，包含上传记录的详细信息
    """
    # 检查文件名
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 读取文件内容
    file_content = await file.read()
    
    # 使用业务服务处理上传
    result = await storage_business_service.upload_file_with_business_logic(
        file_content=file_content,
        filename=file.filename,
        folder='common',
        content_type=file.content_type,
        current_user=current_user
    )
    
    return result


@router.get("/download/{record_id}", summary="下载文件")
async def download_file(
    record_id: int,
):
    """
    通过上传记录ID从存储服务下载文件
    
    Args:
        record_id: 上传记录ID
        
    Returns:
        StreamingResponse: 文件流响应
    """
    # 使用业务服务处理下载
    file_content, upload_record = await storage_business_service.download_file_by_record_id_with_business_logic(
        record_id=record_id
    )
    
    filename_encoded = quote(str(upload_record.original_filename))
    
    return StreamingResponse(
        io.BytesIO(file_content),
        media_type=upload_record.content_type or 'application/octet-stream',
        headers={
            "Content-Disposition": f"attachment; filename*=UTF-8''{filename_encoded}",
            "Content-Length": str(upload_record.file_size)
        }
    )


@router.delete("/{record_id}", summary="删除文件")
async def delete_file(
    record_id: int,
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    通过上传记录ID逻辑删除文件
    
    Args:
        record_id: 上传记录ID
        current_user: 当前用户信息
        
    Returns:
        dict: 删除结果
    """
    # 使用业务服务处理逻辑删除
    result = await storage_business_service.delete_file_record_with_business_logic(
        record_id=record_id, 
        current_user=current_user
    )
    
    return result


@router.get("/info/{record_id}", response_model=FileUploadResponse, summary="获取文件信息")
async def get_file_info(
    record_id: int,
):
    """
    通过上传记录ID获取文件信息
    
    Args:
        record_id: 上传记录ID
        
    Returns:
        FileInfo: 文件信息
    """
    # 使用业务服务处理获取文件信息
    file_info = await storage_business_service.get_file_info_by_record_id_with_business_logic(record_id=record_id)
    
    return file_info


@router.post("/upload-with-extraction", response_model=FileUploadResponse, summary="上传文件并异步提取文字")
async def upload_file_with_extraction(
    file: UploadFile = File(...),
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    上传文件并异步提取文字内容
    
    Args:
        file: 要上传的文件
        current_user: 当前用户信息
        
    Returns:
        FileUploadResponse: 上传响应，文件将在后台异步提取文字
    """
    # 检查文件名
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件类型是否支持提取
    if not file_extractor.is_supported_file(file.filename):
        raise HTTPException(status_code=400, detail=f"不支持的文件类型: {file.filename}")
    
    # 读取文件内容
    file_content = await file.read()
    
    # 使用业务服务处理上传，初始状态为success（上传成功）
    result = await storage_business_service.upload_file_with_business_logic(
        file_content=file_content,
        filename=file.filename,
        folder='common',
        content_type=file.content_type,
        current_user=current_user
    )
    
    # 启动异步文件提取任务
    extraction_started = await storage_business_service.extract_file_content_async(result.id)
    
    if not extraction_started:
        storage_logger.error(f"Failed to start extraction for file {result.id}")
    
    return result


@router.get("/records", response_model=PaginatedResponse[FileUploadResponse], summary="获取文件上传记录")
async def list_upload_records(
    page: int = 1,
    size: int = 50,
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    获取当前用户的文件上传记录列表（分页）
    
    Args:
        page: 页码，默认1
        size: 每页记录数，默认50
        current_user: 当前用户信息
        
    Returns:
        PaginatedResponse[FileUploadResponse]: 分页的文件上传记录列表
    """
    try:
        paginated_data = await get_file_upload_service().list_upload_records_paginated(
            uploader_id=current_user.id,
            page=page,
            size=size
        )
        
        # Convert records to FileUploadResponse
        records = [convert_record_to_response(record) for record in paginated_data["data"]]
        
        response = PaginatedResponse(
            data=records,
            total=paginated_data["total"],
            page=paginated_data["page"],
            size=paginated_data["size"],
            total_pages=paginated_data["total_pages"]
        )
        
        storage_logger.info(f"获取文件上传记录成功: 用户 {current_user.username}")
        return response
        
    except Exception as e:
        storage_logger.error(f"获取文件上传记录时出错: {str(e)}", e)
        raise HTTPException(status_code=500, detail=f"获取文件上传记录时出错: {str(e)}")


@router.get("/{record_id}/extraction-status", summary="获取文件提取状态")
async def get_extraction_status(
    record_id: int,
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    获取文件文字提取状态
    
    Args:
        record_id: 文件记录ID
        current_user: 当前用户信息
        
    Returns:
        dict: 提取状态信息
    """
    try:
        file_upload_service = get_file_upload_service()
        extraction_info = await file_upload_service.get_extraction_info(record_id)
        
        if not extraction_info:
            raise HTTPException(status_code=404, detail=f"文件记录不存在: {record_id}")
        
        # 检查文件权限
        if extraction_info["uploader_id"] and extraction_info["uploader_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权限访问此文件")
        
        # 构建状态响应
        status_response = {
            "record_id": record_id,
            "original_filename": extraction_info["original_filename"],
            "upload_status": extraction_info["upload_status"],
            "extraction_status": extraction_info.get("extraction_status", "pending"),
            "error_message": extraction_info["error_message"],
            "updated_at": extraction_info["updated_at"]
        }
        
        # 如果提取完成，添加额外信息
        if extraction_info.get("extracted_at"):
            status_response["extracted_at"] = extraction_info["extracted_at"]
        
        return status_response
        
    except HTTPException:
        raise
    except Exception as e:
        storage_logger.error(f"获取提取状态时出错: {str(e)}", e)
        raise HTTPException(status_code=500, detail=f"获取提取状态时出错: {str(e)}")


@router.get("/{record_id}/extracted-content", summary="获取文件提取的文字内容")
async def get_extracted_content(
    record_id: int,
    current_user: auth_schemas.User = Depends(get_current_user)
):
    """
    获取文件提取的文字内容
    
    Args:
        record_id: 文件记录ID
        current_user: 当前用户信息
        
    Returns:
        dict: 提取的文字内容
    """
    try:
        file_upload_service = get_file_upload_service()
        extraction_info = await file_upload_service.get_extraction_info(record_id)
        
        if not extraction_info:
            raise HTTPException(status_code=404, detail=f"文件记录不存在: {record_id}")
        
        # 检查文件权限
        if extraction_info["uploader_id"] and extraction_info["uploader_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权限访问此文件")
        
        # 检查提取状态
        try:
            upload_status = UploadStatus.from_string(extraction_info["upload_status"])
            if not upload_status.is_completed:
                raise HTTPException(status_code=400, detail="文件提取尚未完成")
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的文件状态: {extraction_info['upload_status']}")
        
        # 构建内容响应
        content_response = {
            "record_id": record_id,
            "original_filename": extraction_info["original_filename"],
            "upload_status": extraction_info["upload_status"],
            "extraction_status": extraction_info.get("extraction_status", "pending"),
            "extracted_content": extraction_info.get("extracted_content"),
            "extracted_at": extraction_info.get("extracted_at"),
            "error_message": extraction_info["error_message"],
            "updated_at": extraction_info["updated_at"]
        }
        
        return content_response
        
    except HTTPException:
        raise
    except Exception as e:
        storage_logger.error(f"获取提取内容时出错: {str(e)}", e)
        raise HTTPException(status_code=500, detail=f"获取提取内容时出错: {str(e)}")
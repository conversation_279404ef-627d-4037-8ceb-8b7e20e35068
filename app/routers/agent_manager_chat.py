"""
智能体聊天的 FastAPI 路由。

该模块包含用于智能体聊天功能的所有 API 端点，
包括流式聊天、聊天历史管理和标题生成。
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional

from app.logger.logger import get_logger
from app.database.services import ChatService
from app.auth.security import get_current_internal_user
from ..agents.models import (
    ChatHistoryResponse,
    ChatMessageResponse,
    PaginatedResponse
)

# 初始化路由器和日志记录器
router = APIRouter(prefix="/manager/agents/chat", tags=["智能体聊天-管理端"], dependencies=[Depends(get_current_internal_user)])
api_logger = get_logger("llm.chat_api")


@router.get("/history", response_model=PaginatedResponse[ChatHistoryResponse], summary="列出聊天历史")
async def list_chat_histories(
    agent_id: Optional[str] = None, 
    user_id: Optional[str] = None,
    page: int = 1,
    size: int = 50
):
    """列出聊天历史（分页）。"""
    try:
        histories, total_count = await ChatService.list_chat_histories(
            user_id=user_id, agent_id=agent_id, offset=(page - 1) * size, limit=size
        )
        # 转换数据库模型为响应模型，确保包含 user_name 和 user_id 字段
        response_histories = []
        for history in histories:
            history_dict = history.to_dict()
            response_histories.append(ChatHistoryResponse(**history_dict))
        
        # 计算总页数
        total_pages = (total_count + size - 1) // size
        
        # 返回分页响应
        return PaginatedResponse[ChatHistoryResponse](
            data=response_histories,
            total=total_count,
            page=page,
            size=size,
            total_pages=total_pages
        )
    except Exception as e:
        api_logger.error("列出聊天历史失败", exception=e)
        raise HTTPException(status_code=500, detail=f"列出聊天历史时出错: {str(e)}")


@router.get("/history/{session_id}", response_model=ChatHistoryResponse, summary="获取特定的聊天历史")
async def get_chat_history(session_id: str):
    """获取一个特定的聊天历史。"""
    try:
        history = await ChatService.get_chat_history(session_id)
        if not history:
            raise HTTPException(status_code=404, detail=f"未找到聊天历史: {session_id}")
        # 转换数据库模型为响应模型，确保包含 user_name 和 user_id 字段
        history_dict = history.to_dict()
        return ChatHistoryResponse(**history_dict)
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"获取聊天历史 {session_id} 失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取聊天历史时出错: {str(e)}")


@router.delete("/history/{session_id}", summary="逻辑删除会话（软删除）")
async def delete_chat_history(session_id: str):
    try:
        deleted = await ChatService.delete_chat_history(session_id)
        if not deleted:
            raise HTTPException(status_code=404, detail=f"未找到聊天历史: {session_id}")
        return {"success": True}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"逻辑删除聊天历史 {session_id} 失败", exception=e)
        raise HTTPException(status_code=500, detail=f"逻辑删除聊天历史时出错: {str(e)}")


@router.get("/history/{session_id}/messages", response_model=List[ChatMessageResponse], summary="获取聊天会话的消息")
async def get_chat_messages(session_id: str, limit: int = 100):
    """获取一个聊天会话的消息。"""
    try:
        messages = await ChatService.get_messages(session_id, limit=limit)
        return [ChatMessageResponse(**message.to_dict()) for message in messages]
    except Exception as e:
        api_logger.error(f"获取会话 {session_id} 的消息失败", exception=e)
        raise HTTPException(status_code=500, detail=f"获取消息时出错: {str(e)}")
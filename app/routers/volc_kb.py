"""
火山知识库相关的 FastAPI 路由。

该模块提供与火山引擎知识库服务交互的 API 端点。
"""

from typing import Optional, Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form

from app.logger.logger import get_logger
from app.auth.security import get_current_internal_user
from app.auth import schemas as auth_schemas
from app.services import VolcKnowledgeBaseService

router = APIRouter(prefix="/volc-knowledge", tags=["火山知识库"], dependencies=[Depends(get_current_internal_user)])
volc_kb_logger = get_logger("volc.kb.api")

# 全局服务实例
volc_kb_service = VolcKnowledgeBaseService()

@router.post("/bases/{kb_id}/search", summary="搜索文档")
async def search_documents(
    kb_id: int,
    query: str = Form(..., description="搜索查询"),
    limit: int = Form(10, description="返回结果数量限制"),
    current_user: auth_schemas.User = Depends(get_current_internal_user)
):
    """
    在指定的知识库中搜索文档
    
    Args:
        kb_id: 知识库ID
        query: 搜索查询
        limit: 返回结果数量限制
        current_user: 当前用户信息
        
    Returns:
        List[Dict]: 搜索结果列表
    """
    try:
        results = await volc_kb_service.search_documents_with_business_logic(
            kb_id=kb_id,
            query=query,
            limit=limit,
            current_user=current_user
        )
        return results
    except HTTPException:
        raise
    except Exception as e:
        volc_kb_logger.error(f"搜索文档时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索文档时出错: {str(e)}")


@router.delete("/bases/{kb_id}/documents/{doc_id}", summary="删除文档")
async def delete_document(
    kb_id: int,
    doc_id: str,
    current_user: auth_schemas.User = Depends(get_current_internal_user)
):
    """
    从指定的知识库中删除文档
    
    Args:
        kb_id: 知识库ID
        doc_id: 文档ID
        current_user: 当前用户信息
        
    Returns:
        Dict: 删除结果
    """
    try:
        await volc_kb_service.delete_document_with_business_logic(
            kb_id=kb_id,
            doc_id=doc_id,
            current_user=current_user
        )
        return {"message": f"文档删除成功: {doc_id}"}
    except HTTPException:
        raise
    except Exception as e:
        volc_kb_logger.error(f"删除文档时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文档时出错: {str(e)}")


@router.get("/bases/{resource_id}", summary="获取知识库信息")
async def get_knowledge_base_info(
    resource_id: str,
    current_user: auth_schemas.User = Depends(get_current_internal_user)
):
    """
    获取指定知识库的信息
    
    Args:
        resource_id: 知识库ID
        current_user: 当前用户信息
        
    Returns:
        Dict: 知识库信息
    """
    try:
        info = await volc_kb_service.get_knowledge_base_info_with_business_logic(
            resource_id=resource_id
        )
        return info
    except HTTPException:
        raise
    except Exception as e:
        volc_kb_logger.error(f"获取知识库信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取知识库信息时出错: {str(e)}")
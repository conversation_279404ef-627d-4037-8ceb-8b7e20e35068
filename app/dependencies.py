"""
Dependency injection module for Easy Agent Center.

Provides centralized management of service instances and dependencies.
"""

from typing import Optional
from app.crud.file_upload_service import FileUploadService


class ServiceContainer:
    """Container for managing service instances."""
    
    def __init__(self):
        """Initialize the service container."""
        self._file_upload_service: Optional[FileUploadService] = None
    
    @property
    def file_upload_service(self) -> FileUploadService:
        """Get or create the FileUploadService instance."""
        if self._file_upload_service is None:
            self._file_upload_service = FileUploadService()
        return self._file_upload_service


# Global service container instance
service_container = ServiceContainer()


def get_file_upload_service() -> FileUploadService:
    """
    Get the FileUploadService instance.
    
    Returns:
        FileUploadService: The file upload service instance
    """
    return service_container.file_upload_service
"""
Configuration management for Easy Agent Center.

This module handles loading configuration from environment variables and .env files.
"""

import os
from pathlib import Path
from typing import Optional
from dataclasses import dataclass

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    
    # Look for .env file in current directory
    env_path = Path(".env")
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    # python-dotenv not installed, skip loading .env file
    pass


@dataclass
class ServerConfig:
    """Server configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    log_level: str = "info"
    reload: bool = True
    
    @classmethod
    def from_env(cls) -> "ServerConfig":
        """Create server config from environment variables."""
        return cls(
            host=os.getenv("HOST", "0.0.0.0"),
            port=int(os.getenv("PORT", "8000")),
            log_level=os.getenv("LOG_LEVEL", "info").lower(),
            reload=os.getenv("ENVIRONMENT", "development") == "development"
        )


@dataclass
class LegacyLLMConfig:
    """Legacy LLM configuration for backward compatibility."""
    openai_api_key: Optional[str] = None
    default_api_base: Optional[str] = None
    default_api_key: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"

    @classmethod
    def from_env(cls) -> "LegacyLLMConfig":
        """Create LLM config from environment variables."""
        return cls(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            default_api_base=os.getenv("DEFAULT_API_BASE"),
            default_api_key=os.getenv("DEFAULT_API_KEY"),
            default_model=os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo")
        )


@dataclass
class DatabaseConfig:
    """Database configuration."""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: Optional[str] = None
    ale_username:str = ""
    ale_password: Optional[str] = None
    database: str = "easy_agent_center"
    charset: str = "utf8mb4"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    
    @classmethod
    def from_env(cls) -> "DatabaseConfig":
        """Create database config from environment variables."""
        return cls(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "3306")),
            username=os.getenv("DB_USERNAME", "root"),
            password=os.getenv("DB_PASSWORD", ""),
            ale_username=os.getenv("ALEMBIC_DB_USERNAME", ""),
            ale_password=os.getenv("ALEMBIC_DB_PASSWORD", ""),
            database=os.getenv("DB_DATABASE", "easy_agent_center"),
            charset=os.getenv("DB_CHARSET", "utf8mb4"),
            pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
            max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
            pool_timeout=int(os.getenv("DB_POOL_TIMEOUT", "30")),
            pool_recycle=int(os.getenv("DB_POOL_RECYCLE", "3600")),
            echo=os.getenv("DB_ECHO", "false").lower() == "true"
        )
    
    @property
    def database_url(self) -> str:
        """Generate database URL for SQLAlchemy."""
        from urllib.parse import quote_plus
        
        password_encoded = quote_plus(self.password) if self.password else ""
        if password_encoded:
            auth = f"{self.username}:{password_encoded}"
        else:
            auth = self.username
            
        return f"mysql+aiomysql://{auth}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    @property
    def sync_database_url(self) -> str:
        """Generate synchronous database URL for migrations and initial setup."""
        from urllib.parse import quote
        
        # For pymysql, we need to encode special characters in password
        # but not use quote_plus which encodes @ as %40 causing interpolation issues
        password_encoded = quote(self.password, safe='') if self.password else ""
        if password_encoded:
            auth = f"{self.username}:{password_encoded}"
        else:
            auth = self.username
            
        return f"mysql+pymysql://{auth}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    @property
    def ale_database_url(self) -> str:
        """Generate database URL for SQLAlchemy."""
        from urllib.parse import quote_plus
        
        if self.ale_username:
            username = self.ale_username
            password = self.ale_password
        else:
            username = self.username
            password = self.password
            
        password_encoded = quote_plus(password) if password else ""
        if password_encoded:
            auth = f"{username}:{password_encoded}"
        else:
            auth = username
            
        return f"mysql+aiomysql://{auth}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    @property
    def sync_ale_database_url(self) -> str:
        """Generate synchronous database URL for migrations and initial setup."""
        from urllib.parse import quote
        
        # If Alembic credentials are provided, use them; otherwise fall back to regular credentials
        if self.ale_username:
            username = self.ale_username
            password = self.ale_password
        else:
            username = self.username
            password = self.password
            
        # For pymysql, we need to encode special characters in password
        # but not use quote_plus which encodes @ as %40 causing interpolation issues
        password_encoded = quote(password, safe='') if password else ""
        if password_encoded:
            auth = f"{username}:{password_encoded}"
        else:
            auth = username
            
        return f"mysql+pymysql://{auth}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    to_file: bool = True
    directory: str = "logs"
    
    @classmethod
    def from_env(cls) -> "LoggingConfig":
        """Create logging config from environment variables."""
        return cls(
            level=os.getenv("LOG_LEVEL", "INFO").upper(),
            to_file=os.getenv("LOG_TO_FILE", "true").lower() == "true",
            directory=os.getenv("LOG_DIR", "logs")
        )


@dataclass
class ThirdPartyAuthConfig:
    """Third party authentication configuration."""
    base_url: str
    api_url: str
    app_key: str
    passport: str
    password: str

    @classmethod
    def from_env(cls) -> "ThirdPartyAuthConfig":
        """Create third party auth config from environment variables."""
        return cls(
            base_url=os.getenv("THIRD_PARTY_AUTH_BASE_URL", "https://live-api.yanxiu.com"),
            api_url=os.getenv("THIRD_PARTY_API_URL", "https://x-api.3ren.cn/user-hub/oa/login"),
            app_key=os.getenv("THIRD_PARTY_APP_KEY", "wand-3ren"),
            passport=os.getenv("THIRD_PARTY_PASSPORT", ""),
            password=os.getenv("THIRD_PARTY_PASSWORD", "")
        )


@dataclass
class AuthConfig:
    """Authentication configuration."""
    secret_key: str
    third_party: ThirdPartyAuthConfig
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 600

    @classmethod
    def from_env(cls) -> "AuthConfig":
        """Create auth config from environment variables."""
        secret_key = os.getenv("SECRET_KEY")
        if not secret_key:
            raise ValueError("SECRET_KEY environment variable not set.")
        return cls(
            secret_key=secret_key,
            algorithm=os.getenv("ALGORITHM", "HS256"),
            access_token_expire_minutes=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "600")),
            third_party=ThirdPartyAuthConfig.from_env()
        )


@dataclass
class StorageConfig:
    """TOS存储配置"""
    access_key: str
    secret_key: str
    endpoint: str
    region: str
    bucket_name: str
    domain: Optional[str] = None

    @classmethod
    def from_env(cls) -> "StorageConfig":
        """Create storage config from environment variables."""
        return cls(
            access_key=os.getenv("TOS_ACCESS_KEY", ""),
            secret_key=os.getenv("TOS_SECRET_KEY", ""),
            endpoint=os.getenv("TOS_ENDPOINT", ""),
            region=os.getenv("TOS_REGION", ""),
            bucket_name=os.getenv("TOS_BUCKET_NAME", ""),
            domain=os.getenv("TOS_DOMAIN")
        )


@dataclass
class VolcEngineConfig:
    """火山引擎配置"""
    access_key: str
    secret_key: str
    region: str

    @classmethod
    def from_env(cls) -> "VolcEngineConfig":
        """Create VolcEngine config from environment variables."""
        return cls(
            access_key=os.getenv("VOLCENGINE_ACCESS_KEY", ""),
            secret_key=os.getenv("VOLCENGINE_SECRET_KEY", ""),
            region=os.getenv("VOLCENGINE_REGION", "cn-beijing")
        )


@dataclass
class AppConfig:
    """Application configuration."""
    server: ServerConfig
    llm: LegacyLLMConfig  # Keep for backward compatibility
    database: DatabaseConfig
    logging: LoggingConfig
    auth: AuthConfig
    storage: StorageConfig
    volcengine: VolcEngineConfig
    environment: str = "development"
    redis_url: str = "redis://localhost:6379/0"
    redis_cache_ttl: int = 3600  # Cache TTL in seconds (1 hour default)
    mcp_sync_interval: int = 60  # MCP sync interval in seconds (1 minute default)
    cors_origins: str = "*"  # CORS allowed origins (comma-separated)

    @classmethod
    def from_env(cls) -> "AppConfig":
        """Create app config from environment variables."""
        return cls(
            environment=os.getenv("ENVIRONMENT", "development"),
            server=ServerConfig.from_env(),
            llm=LegacyLLMConfig.from_env(),
            database=DatabaseConfig.from_env(),
            logging=LoggingConfig.from_env(),
            auth=AuthConfig.from_env(),
            storage=StorageConfig.from_env(),
            volcengine=VolcEngineConfig.from_env(),
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
            redis_cache_ttl=int(os.getenv("REDIS_CACHE_TTL", "3600")),
            mcp_sync_interval=int(os.getenv("MCP_SYNC_INTERVAL", "60")),
            cors_origins=os.getenv("CORS_ORIGINS", "*")
        )
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.environment == "production"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode."""
        return self.environment == "testing"


# Global configuration instance
config = AppConfig.from_env()


def get_config() -> AppConfig:
    """Get the global configuration instance."""
    return config


def reload_config() -> AppConfig:
    """Reload configuration from environment variables."""
    global config
    config = AppConfig.from_env()
    return config

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    # Look for .env file in current directory
    env_path = Path(".env")
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    # python-dotenv not installed, skip loading .env file
    pass

from app.agents.agent_manager import agent_manager
from app.logger.logger import get_logger, setup_logging
from app.database import get_database_manager
from app.mcp.mcp_manager import mcp_manager
from app.config.config import get_config

from app.llm import llm_router
from app.agents import agent_router, chat_router, manager_chat_router
from app.mcp.api import router as mcp_router
from app.auth.api import router as auth_router
from app.knowledge_base.api import router as knowledge_router
from app.routers.agent_configs import router as agent_configs_router
from app.routers.front.front_agents import router as front_router
from app.routers.storage import router as storage_router
from app.routers.volc_kb import router as volc_kb_router
from app.configs import config_router

# Initialize with a default agent on startup
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app_instance: FastAPI):
    """Initialize the application with database, default agent and default user."""
    # Initialize logging
    setup_logging()
    logger = get_logger("main")
    logger.info("🚀 Starting Easy Agent Center FastAPI application")

    # Initialize database
    db_manager = get_database_manager()
    try:
        await db_manager.initialize()
        logger.info("✅ Database initialized successfully")
        print("✅ Database initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize database", exception=e)
        print(f"⚠️ Warning: Database initialization failed: {e}")


    try:
        # Initialize MCP manager
        await mcp_manager.initialize()
        logger.info("✅ MCP manager initialized successfully")
        print("✅ MCP manager initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize MCP manager", exception=e)
        print(f"⚠️ Warning: MCP manager initialization failed: {e}")

    try:
        # Create default admin user if not exists
        from app.auth.services import UserService
        from app.auth import schemas
        
        db_manager = get_database_manager()
        existing_user = None
        try:
            async with db_manager.get_session() as session:
                existing_user = await UserService.get_user_by_username("admin", session)
        except Exception as db_e:
            logger.warning(f"Could not check database for existing user: {db_e}")

        if existing_user:
            logger.info("✅ Default admin user already exists in database")
            print("✅ Default admin user already exists in database")
        else:
            # Create default admin user
            user_create = schemas.UserCreate(
                username="admin",
                password="admin123"  # Default password, should be changed by user
            )
            await UserService.create_user(user_create)
            logger.info("✅ Default admin user created successfully")
            print("✅ Default admin user created successfully")
    except Exception as e:
        logger.error("Could not create default admin user", exception=e)
        print(f"⚠️ Warning: Could not create default admin user: {e}")

    try:
        # Check if default agent already exists in database
        from app.database.services import AgentService
        db_manager = get_database_manager()

        existing_agent = None
        try:
            async with db_manager.get_session() as session:
                existing_agent = await AgentService.get_agent_by_id("default", session)
        except Exception as db_e:
            logger.warning(f"Could not check database for existing agent: {db_e}")

        if existing_agent:
            logger.info("✅ Default agent already exists in database")
            print("✅ Default agent already exists in database")
        else:
            # Create a default OpenAI agent
            await agent_manager.create_agent(
                agent_id="default",
                name="Default Assistant",
                description="A helpful AI assistant",
                system_prompt="You are a helpful AI assistant. Provide clear and concise responses.",
                set_as_default=True
            )
            logger.info("✅ Default agent created successfully")
            print("✅ Default agent created successfully")
    except Exception as e:
        logger.error("Could not create default agent", exception=e)
        print(f"⚠️ Warning: Could not create default agent: {e}")

    yield

    # Cleanup
    try:
        # Shutdown MCP manager
        await mcp_manager.shutdown_all()
        logger.info("✅ MCP manager shut down")
    except Exception as e:
        logger.error("Error shutting down MCP manager", exception=e)

    try:
        await db_manager.close()
        logger.info("✅ Database connections closed")
    except Exception as e:
        logger.error("Error closing database connections", exception=e)

    logger.info("🛑 Shutting down Easy Agent Center")
    print("👋 Easy Agent Center shutdown complete")

app = FastAPI(
    title="Easy Agent Center",
    description="A FastAPI application for managing and interacting with LlamaIndex agents",
    version="0.1.0",
    lifespan=lifespan
)

# Configure CORS middleware
config = get_config()
origins = config.cors_origins.split(",") if config.cors_origins != "*" else ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Register LLM, Agent, and MCP routers
app.include_router(llm_router)
app.include_router(agent_router)
app.include_router(chat_router)
app.include_router(manager_chat_router)
app.include_router(mcp_router)
app.include_router(auth_router, prefix="/auth", tags=["认证管理"])
app.include_router(knowledge_router)
app.include_router(agent_configs_router)
app.include_router(front_router)
app.include_router(storage_router)
app.include_router(volc_kb_router)
app.include_router(config_router)

@app.get("/")
async def read_root():
    return {
        "message": "Welcome to Easy Agent Center!",
        "description": "A FastAPI application for managing and interacting with LlamaIndex agents",
        "endpoints": {
            "llm_providers": "/llms",
            "agents": "/agents",
            "mcp_servers": "/mcp/servers",
            "chat": "/agents/chat",
            "chat_history": "/agents/chat/history",
            "storage": "/storage"
        }
    }

# Initialize API logger
api_logger = get_logger("api")



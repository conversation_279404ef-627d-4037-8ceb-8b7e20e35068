"""
File upload record service for Easy Agent Center.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, func
from datetime import datetime

from app.database.models import FileUploadRecord
from app.database.connection import get_database_manager
from app.database.enums import UploadStatus
from app.logger.logger import get_logger


class FileUploadService:
    """Service for managing file upload records in the database."""
    
    def __init__(self):
        """Initialize the FileUploadService with required dependencies."""
        self.db_manager = get_database_manager()
        self.logger = get_logger("database.file_upload_service")
    
    async def create_upload_record(
        self,
        original_filename: str,
        file_size: int,
        content_type: Optional[str] = None,
        storage_type: str = "tos",
        object_key: Optional[str] = None,
        file_url: Optional[str] = None,
        upload_status: str = UploadStatus.PENDING.value,
        error_message: Optional[str] = None,
        uploader_id: Optional[str] = None,
        uploader_name: str = "匿名",
        upload_metadata: Optional[dict] = None
    ) -> FileUploadRecord:
        """Create a new file upload record in the database."""
        async with self.db_manager.get_session() as session:
            # Prepare the values for the record
            record_values = {
                "original_filename": original_filename,
                "file_size": file_size,
                "content_type": content_type,
                "storage_type": storage_type,
                "upload_status": upload_status,
                "uploader_id": uploader_id,
                "uploader_name": uploader_name,
            }
            
            # Only add optional fields if they are not None
            if object_key is not None:
                record_values["object_key"] = object_key
            if file_url is not None:
                record_values["file_url"] = file_url
            if error_message is not None:
                record_values["error_message"] = error_message
            if upload_metadata is not None:
                record_values["upload_metadata"] = upload_metadata
            
            # Create new upload record
            upload_record = FileUploadRecord(**record_values)
            
            session.add(upload_record)
            await session.commit()
            await session.refresh(upload_record)
            
            self.logger.info(f"Created file upload record in database: {original_filename}")
            return upload_record
    
    async def update_upload_record(
        self,
        record_id: int,
        **updates
    ) -> Optional[FileUploadRecord]:
        """Update a file upload record."""
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(FileUploadRecord).where(FileUploadRecord.id == record_id)
            )
            upload_record = result.scalar_one_or_none()
            
            if not upload_record:
                return None

            # Handle updates with special care for None values
            for key, value in updates.items():
                if hasattr(upload_record, key):
                    # For JSON fields, we need to be careful with None values
                    if key == "upload_metadata" and value is None:
                        # Set to None directly to ensure it becomes NULL in database
                        setattr(upload_record, key, None)
                    else:
                        setattr(upload_record, key, value)

            await session.commit()
            await session.refresh(upload_record)
            self.logger.info(f"Updated file upload record in database: {record_id}")
            
            return upload_record
    
    async def soft_delete_upload_record(
        self, 
        record_id: int
    ) -> Optional[FileUploadRecord]:
        """Soft delete a file upload record by setting invalid=1.
        
        Args:
            record_id: The record ID to soft delete
            
        Returns:
            Updated FileUploadRecord or None if not found
        """
        async with self.db_manager.get_session() as session:
            # First check if record exists and is not already deleted
            result = await session.execute(
                select(FileUploadRecord).where(
                    FileUploadRecord.id == record_id,
                    FileUploadRecord.invalid == 0
                )
            )
            upload_record = result.scalar_one_or_none()
            
            if not upload_record:
                self.logger.warning(f"Upload record not found or already deleted: {record_id}")
                return None
            
            # Perform soft delete
            upload_record.invalid = 1
            upload_record.updated_at = datetime.now()
            
            await session.commit()
            await session.refresh(upload_record)
            
            self.logger.info(f"Soft deleted file upload record: {record_id}")
            return upload_record
    
    async def get_upload_record_by_id(
        self,
        record_id: int, 
        include_deleted: bool = False
    ) -> Optional[FileUploadRecord]:
        """Get file upload record by ID.
        
        Args:
            record_id: The record ID to fetch
            include_deleted: Whether to include logically deleted records (invalid=1)
            
        Returns:
            FileUploadRecord or None if not found
        """
        async with self.db_manager.get_session() as session:
            query = select(FileUploadRecord).where(FileUploadRecord.id == record_id)
            
            # Filter out deleted records by default
            if not include_deleted:
                query = query.where(FileUploadRecord.invalid == 0)
            
            result = await session.execute(query)
            return result.scalar_one_or_none()
    
    async def list_upload_records(
        self,
        uploader_id: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        include_deleted: bool = False
    ) -> List[FileUploadRecord]:
        """List file upload records with optional filtering by uploader.
        
        Args:
            uploader_id: Filter by uploader ID
            limit: Maximum number of records to return
            offset: Number of records to skip
            include_deleted: Whether to include logically deleted records (invalid=1)
            
        Returns:
            List of FileUploadRecord objects
        """
        async with self.db_manager.get_session() as session:
            query = select(FileUploadRecord)
            
            # Filter out deleted records by default
            if not include_deleted:
                query = query.where(FileUploadRecord.invalid == 0)
            
            if uploader_id:
                query = query.where(FileUploadRecord.uploader_id == uploader_id)
            
            query = query.order_by(FileUploadRecord.created_at.desc()).offset(offset).limit(limit)
            
            result = await session.execute(query)
            return list(result.scalars().all())
    
    async def list_upload_records_paginated(
        self,
        uploader_id: Optional[str] = None,
        page: int = 1,
        size: int = 50,
        include_deleted: bool = False
    ) -> dict:
        """List file upload records with pagination.
        
        Args:
            uploader_id: Filter by uploader ID
            page: Current page number (1-based)
            size: Number of records per page
            include_deleted: Whether to include logically deleted records (invalid=1)
            
        Returns:
            Dictionary with pagination info and records
        """
        async with self.db_manager.get_session() as session:
            # Build base query
            count_query = select(func.count(FileUploadRecord.id))
            data_query = select(FileUploadRecord)
            
            # Apply filters
            if not include_deleted:
                count_query = count_query.where(FileUploadRecord.invalid == 0)
                data_query = data_query.where(FileUploadRecord.invalid == 0)
            
            if uploader_id:
                count_query = count_query.where(FileUploadRecord.uploader_id == uploader_id)
                data_query = data_query.where(FileUploadRecord.uploader_id == uploader_id)
            
            # Get total count
            total_result = await session.execute(count_query)
            total = total_result.scalar()
            
            # Calculate pagination
            offset = (page - 1) * size
            total_pages = (total + size - 1) // size if total and total > 0 else 0
            
            # Get paginated data
            data_query = data_query.order_by(FileUploadRecord.created_at.desc()).offset(offset).limit(size)
            result = await session.execute(data_query)
            records = list(result.scalars().all())
            
            return {
                "data": records,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": total_pages
            }
    
    async def update_extraction_status(
        self, 
        record_id: int,
        status: str,
        extracted_content: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> Optional[FileUploadRecord]:
        """更新文件提取状态和内容
        
        Args:
            record_id: 文件记录ID
            status: 新状态 (extracting, success, failed)
            extracted_content: 提取的文字内容
            error_message: 错误信息
            
        Returns:
            更新后的FileUploadRecord或None
        """
        # 验证状态值
        try:
            UploadStatus.from_string(status)
        except ValueError:
            self.logger.error(f"Invalid upload status: {status}")
            raise ValueError(f"Invalid upload status: {status}")
        
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(FileUploadRecord).where(FileUploadRecord.id == record_id)
            )
            upload_record = result.scalar_one_or_none()
            
            if not upload_record:
                self.logger.warning(f"Upload record not found: {record_id}")
                return None
            
            # 更新状态
            upload_record.upload_status = status
            upload_record.updated_at = datetime.now()
            
            # 更新错误信息
            if error_message:
                upload_record.error_message = error_message
            
            # 更新元数据中的提取内容
            if extracted_content is not None:
                if upload_record.upload_metadata is None:
                    upload_record.upload_metadata = {}
                
                upload_record.upload_metadata.update({
                    "extracted_content": extracted_content,
                    "extracted_at": datetime.now().isoformat(),
                    "extraction_status": status
                })
            
            await session.commit()
            await session.refresh(upload_record)
            
            self.logger.info(f"Updated extraction status for record {record_id}: {status}")
            return upload_record
    
    async def get_extraction_info(self, record_id: int) -> Optional[Dict[str, Any]]:
        """获取文件提取信息
        
        Args:
            record_id: 文件记录ID
            
        Returns:
            提取信息字典或None
        """
        async with self.db_manager.get_session() as session:
            result = await session.execute(
                select(FileUploadRecord).where(FileUploadRecord.id == record_id)
            )
            upload_record = result.scalar_one_or_none()
            
            if not upload_record:
                return None
            
            extraction_info = {
                "id": upload_record.id,
                "original_filename": upload_record.original_filename,
                "upload_status": upload_record.upload_status,
                "uploader_id": upload_record.uploader_id,
                "error_message": upload_record.error_message,
                "updated_at": upload_record.updated_at.isoformat() if upload_record.updated_at else None
            }
            
            # 从元数据中提取相关信息
            if upload_record.upload_metadata:
                extraction_info.update({
                    "extracted_content": upload_record.upload_metadata.get("extracted_content"),
                    "extracted_at": upload_record.upload_metadata.get("extracted_at"),
                    "extraction_status": upload_record.upload_metadata.get("extraction_status")
                })
            
            return extraction_info
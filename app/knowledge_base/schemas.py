"""
Schemas for knowledge base API.
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime


class KnowledgeBaseCreate(BaseModel):
    """Schema for creating knowledge base."""
    name: str = Field(..., min_length=1, max_length=255, description="知识库名称")
    description: Optional[str] = Field(None, max_length=1000, description="知识库描述")
    is_public: bool = Field(False, description="是否为公共知识库")
    relation_kb_id: str = Field("kb-e83714cd728c47ac", description="关联的火山知识库ID")
    config: Optional[Dict[str, Any]] = Field(None, description="知识库配置")


class KnowledgeBaseUpdate(BaseModel):
    """Schema for updating knowledge base."""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="知识库名称")
    description: Optional[str] = Field(None, max_length=1000, description="知识库描述")
    is_public: Optional[bool] = Field(None, description="是否为公共知识库")
    relation_kb_id: Optional[str] = Field(None, description="关联的火山知识库ID")
    config: Optional[Dict[str, Any]] = Field(None, description="知识库配置")


class KnowledgeBaseResponse(BaseModel):
    """Schema for knowledge base response."""
    id: int
    name: str
    description: Optional[str]
    created_by: str
    is_public: bool
    is_active: bool
    relation_kb_id: str
    config: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class DocumentUpload(BaseModel):
    """Schema for document upload."""
    filename: str = Field(..., min_length=1, max_length=500, description="文件名")
    content_type: str = Field(..., description="文件类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="文档元数据")


class DocumentResponse(BaseModel):
    """Schema for document response."""
    id: int
    doc_id: str
    kb_id: int
    filename: str
    file_path: str
    file_size: int
    file_type: str
    content_hash: str
    status: str
    processing_error: Optional[str]
    chunk_count: int
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class DocumentSearch(BaseModel):
    """Schema for document search."""
    query: str = Field(..., min_length=1, description="搜索查询")
    limit: int = Field(10, ge=1, le=100, description="返回结果数量限制")
    doc_ids: Optional[List[str]] = Field(None, description="限制搜索的文档ID列表")


class SearchResult(BaseModel):
    """Schema for search result."""
    content: str
    metadata: Dict[str, Any]
    distance: float
    id: str


class KnowledgeBaseStats(BaseModel):
    """Schema for knowledge base statistics."""
    total_documents: int
    processed_documents: int
    total_chunks: int
    vector_count: int
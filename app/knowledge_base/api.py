"""
知识库管理的API端点。

提供知识库的创建、查询、更新、删除功能，以及文档上传、管理和检索功能。
所有接口都需要内部用户认证。
支持火山引擎知识库集成，可通过relation_kb_id字段关联外部知识库。
"""

import tempfile
import os
from typing import List, Optional
import json

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form

from app.auth.security import get_current_internal_user, get_current_user
from app.auth.models import User
from app.knowledge_base.services import knowledge_base_service, document_service
from app.knowledge_base.schemas import (
    KnowledgeBaseCreate,
    KnowledgeBaseUpdate,
    KnowledgeBaseResponse,
    DocumentResponse
)
from app.services.volc_kb_service import VolcKnowledgeBaseService
from app.logger.logger import get_logger
from app.database.models import KnowledgeBase

logger = get_logger(__name__)
volc_kb_service = VolcKnowledgeBaseService()

router = APIRouter(prefix="/knowledge", tags=["知识库"], dependencies=[Depends(get_current_internal_user)])


@router.post("/bases", response_model=KnowledgeBaseResponse, summary="创建知识库")
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    current_user: User = Depends(get_current_internal_user)
):
    """
    创建新的知识库。
    
    创建一个新的知识库实例，支持设置知识库名称、描述、公开状态
    和关联的火山引擎知识库ID。如果未指定relation_kb_id，
    将使用默认值'kb-e83714cd728c47ac'。
    
    Args:
        kb_data: 知识库创建数据，包含名称、描述、公开状态和关联KB ID
        current_user: 当前认证用户
    
    Returns:
        KnowledgeBaseResponse: 创建的知识库信息
    
    Raises:
        HTTPException(500): 创建失败时返回错误信息
    """
    try:
        knowledge_base = await knowledge_base_service.create_knowledge_base(
            name=kb_data.name,
            description=kb_data.description,
            created_by=current_user.username,
            is_public=kb_data.is_public,
            relation_kb_id=kb_data.relation_kb_id,
            config=kb_data.config
        )
        return KnowledgeBaseResponse(**knowledge_base.to_dict())
    except Exception as e:
        logger.error(f"Failed to create knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases", response_model=List[KnowledgeBaseResponse], summary="获取知识库列表")
async def list_knowledge_bases(
    include_public: bool = True,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """
    获取知识库列表。
    
    分页获取用户可访问的知识库列表，包括用户创建的私有知识库
    和所有公开的知识库。支持分页查询和结果限制。
    
    Args:
        include_public: 是否包含公开知识库，默认为True
        limit: 返回结果数量限制，默认100条，最大100条
        offset: 分页偏移量，默认0
        current_user: 当前认证用户
    
    Returns:
        List[KnowledgeBaseResponse]: 知识库列表，按创建时间倒序排列
    
    Raises:
        HTTPException(500): 查询失败时返回错误信息
    """
    try:
        knowledge_bases = await knowledge_base_service.list_knowledge_bases(
            created_by=current_user.username,
            include_public=include_public,
            limit=limit,
            offset=offset
        )
        return [KnowledgeBaseResponse(**kb.to_dict()) for kb in knowledge_bases]
    except Exception as e:
        logger.error(f"Failed to list knowledge bases: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases/{id}", response_model=KnowledgeBaseResponse, summary="获取知识库详情")
async def get_knowledge_base(
    id: int,
    current_user: User = Depends(get_current_user)
):
    """
    根据ID获取知识库详情。
    
    获取指定ID的知识库详细信息，包括基本信息、配置和关联的
    火山引擎知识库ID。用户只能访问自己创建的私有知识库
    或公开的知识库。
    
    Args:
        id: 知识库ID
        current_user: 当前认证用户
    
    Returns:
        KnowledgeBaseResponse: 知识库详细信息
    
    Raises:
        HTTPException(404): 知识库不存在
        HTTPException(403): 访问权限不足
    """
    knowledge_base = await knowledge_base_service.get_knowledge_base(id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return KnowledgeBaseResponse(**knowledge_base.to_dict())


@router.put("/bases/{id}", response_model=KnowledgeBaseResponse, summary="更新知识库")
async def update_knowledge_base(
    id: int,
    kb_data: KnowledgeBaseUpdate,
    current_user: User = Depends(get_current_user)
):
    """
    更新知识库信息。
    
    更新指定知识库的基本信息，包括名称、描述、公开状态
    和关联的火山引擎知识库ID。只有知识库的创建者才能更新。
    未指定的字段将保持原值不变。
    
    Args:
        id: 知识库ID
        kb_data: 知识库更新数据，所有字段都是可选的
        current_user: 当前认证用户
    
    Returns:
        KnowledgeBaseResponse: 更新后的知识库信息
    
    Raises:
        HTTPException(404): 知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(500): 更新失败时返回错误信息
    """
    knowledge_base = await knowledge_base_service.get_knowledge_base(id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check ownership
    if knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        updated_kb = await knowledge_base_service.update_knowledge_base(
            id=id,
            name=kb_data.name,
            description=kb_data.description,
            is_public=kb_data.is_public,
            relation_kb_id=kb_data.relation_kb_id,
            config=kb_data.config
        )
        if not updated_kb:
            raise HTTPException(status_code=404, detail="Knowledge base not found")
        return KnowledgeBaseResponse(**updated_kb.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/bases/{id}", summary="删除知识库")
async def delete_knowledge_base(
    id: int,
    current_user: User = Depends(get_current_user)
):
    """
    逻辑删除知识库。
    
    通过设置invalid字段为1来逻辑删除指定ID的知识库，而不是物理删除。
    此操作可以保留数据以便潜在的恢复，只有知识库的创建者才能执行删除。
    关联的文档和向量化数据会保留在系统中但不可访问。
    
    Args:
        id: 知识库ID
        current_user: 当前认证用户
    
    Returns:
        dict: 包含成功消息的字典
    
    Raises:
        HTTPException(404): 知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(500): 删除失败时返回错误信息
    """
    knowledge_base = await knowledge_base_service.get_knowledge_base(id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check ownership
    if knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        success = await knowledge_base_service.delete_knowledge_base(id=id)
        if success:
            return {"message": "Knowledge base deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Knowledge base not found")
    except Exception as e:
        logger.error(f"Failed to delete knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _validate_upload_document(
    knowledge_base: Optional[KnowledgeBase],
    current_user: User,
    file: UploadFile
):
    """
    Validate the document upload request.
    """
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")

    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="Invalid file")

    content = await file.read()
    # Check file size (limit to 50MB)
    if len(content) > 50 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File too large (max 50MB)")

    # Validate file type
    allowed_types = [
        'text/plain',
        'text/markdown',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Unsupported file type")


@router.post("/bases/{kb_id}/documents", response_model=DocumentResponse, summary="上传文档")
async def upload_document(
    kb_id: int,
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """
    上传文档到知识库。
    
    先根据kb_id获取relation_kb_id，然后将文档上传到火山引擎知识库中，
    获取doc_id后存储到本地documents表中。支持多种格式：文本、Markdown、PDF、
    Word、PowerPoint、Excel等。文件大小限制为10MB。
    
    Args:
        kb_id: 目标知识库ID
        file: 要上传的文档文件
        metadata: 文档元数据JSON字符串，可选
        current_user: 当前认证用户
    
    Returns:
        DocumentResponse: 上传的文档信息
    
    Raises:
        HTTPException(404): 知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(400): 文件无效、过大或不支持类型
        HTTPException(500): 上传失败时返回错误信息
    """
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    await _validate_upload_document(knowledge_base, current_user, file)
    content = await file.read()
    
    try:
        assert knowledge_base is not None
        assert file.filename is not None
        
        metadata_dict = json.loads(metadata) if metadata else {}
        
        # Create temporary file for upload
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Upload document using Volcengine service
            upload_result = await volc_kb_service.upload_document_with_business_logic(
                kb_id=kb_id,
                volc_kb_id=knowledge_base.relation_kb_id,
                file_path=temp_file_path,
                filename=file.filename,
                metadata=metadata_dict,
                current_user=current_user
            )
            
            if upload_result.success and upload_result.db_persisted:
                # Get the document from database using the returned doc_id
                db_doc_id = upload_result.db_doc_id
                if db_doc_id:
                    document = await document_service.get_document(str(db_doc_id))
                    if document:
                        return DocumentResponse(**document.to_dict())
                    else:
                        raise HTTPException(status_code=500, detail="Document created but not found in database")
                else:
                    raise HTTPException(status_code=500, detail="Document upload succeeded but no doc_id returned")
            else:
                error_msg = upload_result.error or "Unknown error"
                raise HTTPException(status_code=500, detail=f"文档上传失败: {error_msg}")
        
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {temp_file_path}: {e}")
    
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bases/{kb_id}/documents", response_model=List[DocumentResponse], summary="获取文档列表")
async def list_documents(
    kb_id: int,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """
    获取知识库中的文档列表。
    
    分页获取指定知识库中的文档列表，支持按状态筛选和分页查询。
    可返回所有文档或特定状态的文档（如处理中、已完成、失败等）。
    
    Args:
        kb_id: 知识库ID
        status: 文档状态筛选，如'pending'、'processed'、'failed'等，可选
        limit: 返回结果数量限制，默认100条
        offset: 分页偏移量，默认0
        current_user: 当前认证用户
    
    Returns:
        List[DocumentResponse]: 文档列表，包含文档基本信息和处理状态
    
    Raises:
        HTTPException(404): 知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(500): 查询失败时返回错误信息
    """
    knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    # Check access
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        documents = await document_service.list_documents(
            kb_id=kb_id,
            status=status,
            limit=limit,
            offset=offset
        )
        return [DocumentResponse(**doc.to_dict()) for doc in documents]
    except Exception as e:
        logger.error(f"Failed to list documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/documents/{doc_id}", response_model=DocumentResponse, summary="获取文档详情")
async def get_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    根据ID获取文档详情。
    
    获取指定文档的详细信息，包括文件信息、处理状态、
    分块数量等。用户只能访问自己有权限的知识库中的文档。
    
    Args:
        doc_id: 文档ID
        current_user: 当前认证用户
    
    Returns:
        DocumentResponse: 文档详细信息
    
    Raises:
        HTTPException(404): 文档不存在
        HTTPException(404): 关联知识库不存在
        HTTPException(403): 访问权限不足
    """
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return DocumentResponse(**document.to_dict())


@router.delete("/documents/{doc_id}", summary="删除文档")
async def delete_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    删除文档。
    
    删除指定文档及其相关的向量化数据。
    此操作不可逆，用户只能删除自己有权限的知识库中的文档。
    
    Args:
        doc_id: 文档ID
        current_user: 当前认证用户
    
    Returns:
        dict: 包含成功消息的字典
    
    Raises:
        HTTPException(404): 文档不存在
        HTTPException(404): 关联知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(500): 删除失败时返回错误信息
    """
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        success = await document_service.delete_document(doc_id)
        if success:
            return {"message": "Document deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Document not found")
    except Exception as e:
        logger.error(f"Failed to delete document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/documents/{doc_id}/reprocess", summary="重新处理文档")
async def reprocess_document(
    doc_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    重新处理文档。
    
    对已上传的文档重新进行向量化处理。会将文档状态重置为
    'pending'并重新启动处理流程。适用于文档处理失败或需要
    更新向量化数据的场景。
    
    Args:
        doc_id: 文档ID
        current_user: 当前认证用户
    
    Returns:
        dict: 包含处理启动消息的字典
    
    Raises:
        HTTPException(404): 文档不存在
        HTTPException(404): 关联知识库不存在
        HTTPException(403): 访问权限不足
        HTTPException(500): 重新处理启动失败时返回错误信息
    """
    document = await document_service.get_document(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check access
    knowledge_base = await knowledge_base_service.get_knowledge_base(document.kb_id)
    if not knowledge_base:
        raise HTTPException(status_code=404, detail="Knowledge base not found")
    
    if not knowledge_base.is_public and knowledge_base.created_by != current_user.username:
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        # Reset document status
        await document_service.update_document_status(doc_id, "pending")
        
        # Note: Document reprocessing logic would need to be implemented
        # using the Volcengine service for reprocessing uploaded documents
        logger.info(f"Document status reset to pending for reprocessing: {doc_id}")
        
        return {"message": "Document reprocessing started"}
    except Exception as e:
        logger.error(f"Failed to reprocess document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

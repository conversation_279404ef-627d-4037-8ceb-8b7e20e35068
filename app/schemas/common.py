"""
Common schemas for Easy Agent Center.

Provides shared schema definitions used across different modules.
"""

from typing import List, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class PaginatedResponse(BaseModel, Generic[T]):
    """通用分页响应模型"""
    
    data: List[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页记录数")
    total_pages: int = Field(..., description="总页数")
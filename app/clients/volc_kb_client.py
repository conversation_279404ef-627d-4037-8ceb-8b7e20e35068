"""
火山引擎知识库客户端模块

提供与火山引擎ARK知识库服务的集成接口。
"""

import os
import json
from typing import Optional, Dict, Any, List
from datetime import datetime

from volcengine.viking_knowledgebase import VikingKnowledgeBaseService, Collection

from app.logger.logger import get_logger
from app.config.config import get_config
from app.clients.schemas import VolcKnowledgeBaseUploadResponse


class VolcKnowledgeBaseClient:
    """火山引擎知识库客户端类，提供与火山引擎ARK知识库服务的集成接口"""
    
    def __init__(self):
        """
        初始化火山引擎知识库客户端
        """
        self.logger = get_logger("volc.kb.client")
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化火山引擎客户端"""
        try:
            config = get_config()
            volc_config = config.volcengine
            
            # 验证配置完整性
            if not volc_config.access_key or not volc_config.secret_key:
                self.logger.error("VOLCENGINE_ACCESS_KEY 和 VOLCENGINE_SECRET_KEY 环境变量必须设置")
                raise ValueError("火山引擎 API 密钥未配置，请检查环境变量")
            
            # 创建火山知识库服务客户端
            self.client = VikingKnowledgeBaseService(
                host="api-knowledgebase.mlp.cn-beijing.volces.com",
                scheme="https",
                connection_timeout=30,
                socket_timeout=30
            )
            
            # 设置认证信息
            self.client.set_ak(volc_config.access_key)
            self.client.set_sk(volc_config.secret_key)
            
            self.logger.info("火山引擎知识库客户端初始化成功")
        except Exception as e:
            self.logger.error(f"火山引擎知识库客户端初始化失败: {str(e)}")
            # 即使初始化失败，也继续运行，使用模拟实现
    
    def _get_file_type(self, filename: str) -> str:
        """
        根据文件名获取文件类型
        
        Args:
            filename: 文件名
            
        Returns:
            str: 文件类型
        """
        import os
        _, ext = os.path.splitext(filename)
        ext = ext.lower()
        
        # 常见文件类型映射：https://www.volcengine.com/docs/84313/1339026
        type_map = {
            '.txt': 'txt',
            '.md': 'markdown',
            '.pdf': 'pdf',
            '.doc': 'doc',
            '.docx': 'docx',
            '.xls': 'xls',
            '.xlsx': 'xlsx',
            '.ppt': 'ppt',
            '.pptx': 'pptx'
        }
        
        return type_map.get(ext, 'file')
    
    
    async def upload_document_by_url(
        self,
        resource_id: str,
        file_url: str,
        filename: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> VolcKnowledgeBaseUploadResponse:
        """
        通过URL上传文档到知识库
        
        Args:
            resource_id: 知识库ID
            file_url: 文件URL
            filename: 文件名
            metadata: 文档元数据
            
        Returns:
            VolcKnowledgeBaseUploadResponse: 文档上传结果
        """
        try:
            if self.client:
                # 先验证知识库是否存在
                try:
                    collection = await self.get_knowledge_base_info(resource_id)
                except Exception as e:
                    error_msg = f"知识库 {resource_id} 不存在或无法访问: {str(e)}"
                    self.logger.error(error_msg)
                    return VolcKnowledgeBaseUploadResponse.error_response(error_msg)
                
                # 使用Collection对象的方式上传文档，更符合火山引擎的最佳实践
                
                # 生成唯一的文档ID
                import uuid
                doc_id = "doc_" + str(uuid.uuid4()).replace("-", "")
                
                # 使用Collection的add_doc方法通过URL上传文档
                collection.add_doc(
                    add_type="url",
                    doc_id=doc_id,
                    doc_name=filename,
                    doc_type=self._get_file_type(filename),
                    url=file_url,
                    resource_id=resource_id
                )
                
                self.logger.info(f"成功通过URL上传文档到知识库 {resource_id}: {filename}")
                return VolcKnowledgeBaseUploadResponse.success_response(
                    doc_id=doc_id,
                    kb_id=resource_id,
                    filename=filename,
                    file_url=file_url,
                    status="uploaded"
                )
            else:
                # 客户端未初始化，返回错误
                error_msg = "火山引擎知识库客户端未正确初始化"
                self.logger.error(error_msg)
                return VolcKnowledgeBaseUploadResponse.error_response(error_msg)
        except Exception as e:
            error_msg = f"通过URL上传文档失败: {str(e)}"
            self.logger.error(error_msg, e)
            return VolcKnowledgeBaseUploadResponse.error_response(error_msg)
    
    async def search_documents(
        self,
        kb_id: str,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        在知识库中搜索文档
        
        Args:
            kb_id: 知识库ID（对应SDK中的collection_name）
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            if self.client:
                # 先验证知识库是否存在
                try:
                    await self.get_knowledge_base_info(kb_id)
                except Exception as e:
                    error_msg = f"知识库 {kb_id} 不存在或无法访问: {str(e)}"
                    self.logger.error(error_msg)
                    return []
                
                # 实现真实的火山引擎API调用
                # 使用SearchCollection API搜索文档
                points = self.client.search_collection(collection_name=kb_id
                                                       , query=query
                                                       , limit=limit
                                                       , project='live')
                
                self.logger.info(f"在知识库 {kb_id} 中搜索: {query}")
                return points
            else:
                # 客户端未初始化，返回错误
                self.logger.error("火山引擎知识库客户端未正确初始化")
                return []
        except Exception as e:
            error_msg = f"搜索文档失败: {str(e)}"
            self.logger.error(error_msg, e)
            return []
    
    async def delete_document(self, kb_id: str, doc_id: str) -> bool:
        """
        从知识库中删除文档
        
        Args:
            kb_id: 知识库ID（对应SDK中的collection_name）
            doc_id: 文档ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if self.client:
                # 先验证知识库是否存在
                try:
                    await self.get_knowledge_base_info(kb_id)
                except Exception as e:
                    error_msg = f"知识库 {kb_id} 不存在或无法访问: {str(e)}"
                    self.logger.error(error_msg)
                    return False
                
                # 实现真实的火山引擎API调用
                # 使用DeleteDoc API删除文档
                params = {
                    "collection_name": kb_id,
                    "doc_id": doc_id,
                    "project": "default"
                }
                
                self.client.json_exception("DeleteDoc", {}, json.dumps(params))
                
                self.logger.info(f"从知识库 {kb_id} 中删除文档: {doc_id}")
                return True
            else:
                # 客户端未初始化
                self.logger.error("火山引擎知识库客户端未正确初始化")
                return False
        except Exception as e:
            error_msg = f"删除文档失败: {str(e)}"
            self.logger.error(error_msg)
            return False
    
    async def get_knowledge_base_info(self, resource_id: str) -> Collection:
        """
        获取知识库信息
         
        Args:
            resource_id: 知识库ID（
            
        Returns:
            Collection: 知识库信息
        """
        if self.client:
            # 实现真实的火山引擎API调用
            collection = self.client.get_collection(collection_name=None, resource_id=resource_id)
            
            self.logger.info(f"获取知识库信息: {resource_id}")
            return collection
        else:
            # 客户端未初始化，抛出异常
            error_msg = "火山引擎知识库客户端未正确初始化"
            self.logger.error(error_msg)
            raise Exception(error_msg)
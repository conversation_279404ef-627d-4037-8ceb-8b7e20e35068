"""
客户端相关的数据模型

定义客户端使用的配置和数据结构。
"""

from datetime import datetime
from typing import Optional, List, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class StorageConfig(BaseModel):
    """TOS存储配置模型"""
    
    access_key: str = Field(..., description="TOS访问密钥")
    secret_key: str = Field(..., description="TOS密钥")
    endpoint: str = Field(..., description="TOS服务端点")
    region: str = Field(..., description="TOS区域")
    bucket_name: str = Field(..., description="存储桶名称")
    domain: Optional[str] = Field(None, description="TOS访问域名")
    
    class Config:
        env_prefix = "TOS_"


class FileUploadResponse(BaseModel):
    """文件上传响应模型，基于FileUploadRecord的安全响应"""
    
    id: int = Field(..., description="上传记录ID")
    original_filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    storage_type: str = Field(..., description="存储类型")
    object_key: Optional[str] = Field(None, description="对象存储中的键名")
    file_url: Optional[str] = Field(None, description="文件访问URL")
    upload_status: str = Field(..., description="上传状态 (pending: 待处理, extracting: 提取中, success: 成功, failed: 失败)")
    error_message: Optional[str] = Field(None, description="错误信息")
    uploader_name: str = Field(..., description="上传者名称")
    file_extension: str = Field(..., description="文件扩展名")
    readable_size: str = Field(..., description="可读的文件大小")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class UploadResult(BaseModel):
    """文件上传结果模型"""
    
    file_url: Optional[str] = Field(None, description="文件访问URL")
    object_key: Optional[str] = Field(None, description="TOS对象键")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    etag: Optional[str] = Field(None, description="文件ETag")
    upload_time: datetime = Field(..., description="上传时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class FileInfo(BaseModel):
    """文件信息模型"""
    
    object_key: str = Field(..., description="TOS对象键")
    file_size: int = Field(..., description="文件大小（字节）")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    etag: str = Field(..., description="文件ETag")
    last_modified: datetime = Field(..., description="最后修改时间")
    
    @property
    def file_name(self) -> str:
        """从对象键中提取文件名"""
        return self.object_key.split("/")[-1]
    
    @property
    def file_extension(self) -> str:
        """获取文件扩展名"""
        return self.file_name.split(".")[-1] if "." in self.file_name else ""
    
    @property
    def readable_size(self) -> str:
        """获取可读的文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
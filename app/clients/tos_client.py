"""
TOS客户端初始化和管理模块

基于火山引擎TOS Python SDK的客户端封装，
提供TOS服务的连接和基本配置。
"""

import tos
from typing import Optional
from app.logger.logger import get_logger
from .models import StorageConfig


class TOSClient:
    """TOS客户端管理类"""
    
    def __init__(self, config: StorageConfig):
        """
        初始化TOS客户端
        
        Args:
            config: TOS存储配置
        """
        self.config = config
        self.logger = get_logger("storage.tos_client")
        self._client: Optional[tos.TosClientV2] = None
        
    def _initialize_client(self) -> tos.TosClientV2:
        """初始化TOS客户端实例"""
        try:
            client = tos.TosClientV2(
                ak=self.config.access_key,
                sk=self.config.secret_key,
                endpoint=self.config.endpoint,
                region=self.config.region
            )
            self.logger.info(f"TOS客户端初始化成功，区域: {self.config.region}")
            return client
        except Exception as e:
            self.logger.error(f"TOS客户端初始化失败: {str(e)}")
            raise
    
    @property
    def client(self) -> tos.TosClientV2:
        """获取TOS客户端实例（延迟初始化）"""
        if self._client is None:
            self._client = self._initialize_client()
        return self._client
    
    def test_connection(self) -> bool:
        """测试TOS连接是否正常"""
        try:
            # 尝试列出bucket来测试连接
            self.client.list_buckets()
            self.logger.info("TOS连接测试成功")
            return True
        except Exception as e:
            self.logger.error(f"TOS连接测试失败: {str(e)}")
            return False
    
    def close(self):
        """关闭TOS客户端连接"""
        if self._client:
            try:
                self._client.close()
                self._client = None
                self.logger.info("TOS客户端连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭TOS客户端连接失败: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
"""
存储客户端模块

提供文件上传、下载、删除等高级存储操作的统一接口。
"""

import os
import uuid
from typing import BinaryIO, Optional
from datetime import datetime

from app.logger.logger import get_logger
from ..clients.tos_client import TOSClient
from ..clients.models import StorageConfig, UploadResult, FileInfo


class StorageClient:
    """存储客户端类，提供文件操作的高级接口"""
    
    def __init__(self, config: StorageConfig):
        """
        初始化存储客户端
        
        Args:
            config: 存储配置
        """
        self.config = config
        self.logger = get_logger("storage.client")
        self.tos_client = TOSClient(config)
    
    async def upload_file(
        self, 
        file_data: BinaryIO, 
        filename: str,
        folder: Optional[str] = None,
        content_type: Optional[str] = None
    ) -> UploadResult:
        """
        上传文件到TOS
        
        Args:
            file_data: 文件数据流
            filename: 原始文件名
            folder: 存储文件夹路径（可选）
            content_type: 文件MIME类型（可选）
            
        Returns:
            UploadResult: 上传结果
        """
        try:
            # 生成唯一的文件key
            file_ext = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            
            if folder:
                object_key = f"{folder.strip('/')}/{unique_filename}"
            else:
                object_key = unique_filename
            
            # 读取文件内容
            file_data.seek(0)
            content = file_data.read()
            
            # 上传到TOS
            put_kwargs = {
                "bucket": self.config.bucket_name,
                "key": object_key,
                "content": content
            }
            if content_type:
                put_kwargs["content_type"] = content_type
                
            response = self.tos_client.client.put_object(**put_kwargs)
            
            # 生成文件URL
            if self.config.domain:
                # 使用配置的域名
                file_url = f"https://{self.config.domain}/{object_key}"
            else:
                # 使用默认格式
                file_url = f"https://{self.config.bucket_name}.{self.config.endpoint}/{object_key}"
            
            upload_result = UploadResult(
                file_url=file_url,
                object_key=object_key,
                file_size=len(content),
                etag=response.etag,
                upload_time=datetime.now(),
                error_message=None
            )
            
            self.logger.info(f"文件上传成功: {object_key}")
            return upload_result
            
        except Exception as e:
            self.logger.error(f"文件上传失败: {str(e)}", e)
            raise
    
    async def download_file(self, object_key: str) -> Optional[bytes]:
        """
        从TOS下载文件
        
        Args:
            object_key: TOS对象键
            
        Returns:
            bytes: 文件内容，失败时返回None
        """
        try:
            response = self.tos_client.client.get_object(
                bucket=self.config.bucket_name,
                key=object_key
            )
            
            content = response.read()
            if isinstance(content, bytes):
                self.logger.info(f"文件下载成功: {object_key}")
                return content
            else:
                self.logger.error(f"文件下载返回非字节类型: {object_key}")
                return None
            
        except Exception as e:
            self.logger.error(f"文件下载失败: {object_key}, 错误: {str(e)}", e)
            return None
    
    async def delete_file(self, object_key: str) -> bool:
        """
        从TOS删除文件
        
        Args:
            object_key: TOS对象键
            
        Returns:
            bool: 删除是否成功
        """
        try:
            self.tos_client.client.delete_object(
                bucket=self.config.bucket_name,
                key=object_key
            )
            
            self.logger.info(f"文件删除成功: {object_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件删除失败: {object_key}, 错误: {str(e)}", e)
            return False
    
    async def get_file_info(self, object_key: str) -> Optional[FileInfo]:
        """
        获取文件信息
        
        Args:
            object_key: TOS对象键
            
        Returns:
            FileInfo: 文件信息，失败时返回None
        """
        try:
            response = self.tos_client.client.head_object(
                bucket=self.config.bucket_name,
                key=object_key
            )
            
            file_info = FileInfo(
                object_key=object_key,
                file_size=response.content_length or 0,
                content_type=response.content_type,
                etag=response.etag or "",
                last_modified=response.last_modified or datetime.now()
            )
            
            self.logger.info(f"获取文件信息成功: {object_key}")
            return file_info
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {object_key}, 错误: {str(e)}", e)
            return None
    
    def close(self):
        """关闭存储客户端"""
        self.tos_client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        return False

"""
Schemas for client API responses.
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class VolcKnowledgeBaseUploadResponse(BaseModel):
    """Schema for Volcengine knowledge base document upload response."""
    success: bool = Field(..., description="上传是否成功")
    doc_id: Optional[str] = Field(None, description="火山引擎文档ID")
    kb_id: Optional[str] = Field(None, description="知识库ID")
    filename: Optional[str] = Field(None, description="文件名")
    file_url: Optional[str] = Field(None, description="文件URL")
    status: Optional[str] = Field(None, description="上传状态")
    uploaded_at: Optional[str] = Field(None, description="上传时间")
    error: Optional[str] = Field(None, description="错误信息")
    
    @classmethod
    def success_response(
        cls, 
        doc_id: str, 
        kb_id: str, 
        filename: str, 
        file_url: str, 
        status: str = "uploaded"
    ) -> "VolcKnowledgeBaseUploadResponse":
        """Create a successful upload response."""
        return cls(
            success=True,
            doc_id=doc_id,
            kb_id=kb_id,
            filename=filename,
            file_url=file_url,
            status=status,
            uploaded_at=datetime.now().isoformat(),
            error=None
        )
    
    @classmethod
    def error_response(cls, error_message: str) -> "VolcKnowledgeBaseUploadResponse":
        """Create an error response."""
        return cls(
            success=False,
            doc_id=None,
            kb_id=None,
            filename=None,
            file_url=None,
            status=None,
            uploaded_at=None,
            error=error_message
        )


class DocumentUploadBusinessResponse(BaseModel):
    """Schema for business layer document upload response."""
    success: bool = Field(..., description="上传是否成功")
    doc_id: Optional[str] = Field(None, description="火山引擎文档ID")
    kb_id: Optional[str] = Field(None, description="知识库ID")
    filename: Optional[str] = Field(None, description="文件名")
    file_url: Optional[str] = Field(None, description="文件URL")
    status: Optional[str] = Field(None, description="上传状态")
    uploaded_at: Optional[str] = Field(None, description="上传时间")
    db_persisted: bool = Field(False, description="数据库持久化是否成功")
    db_doc_id: Optional[str] = Field(None, description="数据库文档ID")
    error: Optional[str] = Field(None, description="错误信息")
    
    @classmethod
    def success_response(
        cls,
        volc_response: VolcKnowledgeBaseUploadResponse,
        db_doc_id: str
    ) -> "DocumentUploadBusinessResponse":
        """Create a successful business upload response."""
        return cls(
            success=volc_response.success,
            doc_id=volc_response.doc_id,
            kb_id=volc_response.kb_id,
            filename=volc_response.filename,
            file_url=volc_response.file_url,
            status=volc_response.status,
            uploaded_at=volc_response.uploaded_at,
            db_persisted=True,
            db_doc_id=db_doc_id,
            error=volc_response.error
        )
    
    @classmethod
    def partial_success_response(
        cls,
        volc_response: VolcKnowledgeBaseUploadResponse
    ) -> "DocumentUploadBusinessResponse":
        """Create a partial success response (Volcengine success, DB failure)."""
        return cls(
            success=volc_response.success,
            doc_id=volc_response.doc_id,
            kb_id=volc_response.kb_id,
            filename=volc_response.filename,
            file_url=volc_response.file_url,
            status=volc_response.status,
            uploaded_at=volc_response.uploaded_at,
            db_persisted=False,
            db_doc_id=None,
            error=volc_response.error
        )
    
    @classmethod
    def error_response(cls, error_message: str) -> "DocumentUploadBusinessResponse":
        """Create an error response."""
        return cls(
            success=False,
            doc_id=None,
            kb_id=None,
            filename=None,
            file_url=None,
            status=None,
            uploaded_at=None,
            db_persisted=False,
            db_doc_id=None,
            error=error_message
        )


class VolcKnowledgeBaseInfo(BaseModel):
    """Schema for Volcengine knowledge base information."""
    id: str = Field(..., description="知识库ID")
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(None, description="知识库描述")
    doc_num: int = Field(0, description="文档数量")
    project: str = Field(..., description="项目名称")
    update_time: Optional[str] = Field(None, description="更新时间")
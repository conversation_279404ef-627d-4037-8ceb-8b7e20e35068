"""
Database services for user authentication.
"""
from typing import Optional, List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
import httpx

from app.auth.models import User
from app.auth import schemas, security
from app.database.connection import get_database_manager
from app.cache.base_cache import BaseRedis<PERSON><PERSON>, SerializationStrategy


class UserService:
    """Service for managing users in the database."""

    @staticmethod
    async def get_user_by_username(username: str, session: Optional[AsyncSession] = None) -> Optional[User]:
        """Get user by username."""
        if session:
            result = await session.execute(
                select(User).where(User.username == username)
            )
            return result.scalar_one_or_none()
        else:
            db_manager = get_database_manager()
            async with db_manager.get_session() as session:
                result = await session.execute(
                    select(User).where(User.username == username)
                )
                return result.scalar_one_or_none()

    @staticmethod
    async def create_user(user: schemas.UserCreate) -> User:
        """Create a new user."""
        if user.username is None:
            raise ValueError("Username is required")
            
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            existing_user = await UserService.get_user_by_username(user.username, session)
            if existing_user:
                raise ValueError(f"User with username '{user.username}' already exists")

            hashed_password = security.get_password_hash(user.password)
            db_user = User(username=user.username, hashed_password=hashed_password)
            
            session.add(db_user)
            await session.commit()
            await session.refresh(db_user)
            return db_user

    @staticmethod
    async def get_all_users() -> List[User]:
        """Get all users."""
        db_manager = get_database_manager()
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(User).order_by(User.created_at.desc())
            )
            return list(result.scalars().all())


class ThirdPartyTokenService:
    """Service for managing third-party admin tokens."""
    
    def __init__(self):
        """Initialize with Redis cache for token storage."""
        self.cache = BaseRedisCache(
            key_prefix="easy-agent-center:third-party-token:",
            default_ttl=86400,  # 1 day
            serialization=SerializationStrategy.JSON
        )
    
    async def get_admin_token(self) -> Optional[str]:
        """Get third-party admin token from cache or fetch from API."""
        # Try cache first
        token = await self.cache.get("admin_token")
        if token:
            return token
        
        # Fetch from API if not in cache
        token = await self._fetch_token_from_api()
        if token:
            # Store in cache with 1 day TTL
            await self.cache.set("admin_token", token)
        
        return token
    
    async def _fetch_token_from_api(self) -> Optional[str]:
        """Fetch admin token from third-party API."""
        from app.config.config import get_config
        
        config = get_config()
        url = config.auth.third_party.api_url
        headers = {
            'Accept': 'application/json, text/plain, */*'
        }
        data = {
            "appKey": config.auth.third_party.app_key,
            "passport": config.auth.third_party.passport,
            "password": config.auth.third_party.password
        }
        
        # Check if required configuration is available
        if not config.auth.third_party.passport or not config.auth.third_party.password:
            print("Third-party authentication credentials not configured")
            return None
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response.raise_for_status()
                result = response.json()
                
                # Extract token from response (adjust based on actual API response format)
                if 'data' in result and 'access_token' in result['data']:
                    return result['data']['access_token']
                else:
                    return None
                    
        except Exception as e:
            # Log error but don't raise to avoid breaking the service
            print(f"Failed to fetch third-party admin token: {e}")
            return None


# Global instance
third_party_token_service = ThirdPartyTokenService()

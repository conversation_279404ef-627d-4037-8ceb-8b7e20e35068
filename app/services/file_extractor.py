"""
File content extraction service for Easy Agent Center.

该模块提供文件内容提取功能，支持从不同类型的文件中提取文本内容。
"""

import os
import asyncio
import tempfile
from typing import Optional, Dict, Any, List
from pathlib import Path

from app.logger.logger import get_logger
from app.crud.file_upload_service import FileUploadService
from docx import Document
from openpyxl import load_workbook
try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None


class FileExtractor:
    """文件内容提取器"""
    
    def __init__(self):
        """初始化文件提取器"""
        self.logger = get_logger("services.file_extractor")
        self.file_upload_service = FileUploadService()
        
        # 支持的文件类型映射
        self.supported_extensions = {
            '.txt': self._extract_text,
            '.log': self._extract_text,
            '.md': self._extract_markdown,
            '.markdown': self._extract_markdown,
            '.doc': self._extract_word,
            '.docx': self._extract_word,
            '.xls': self._extract_excel,
            '.xlsx': self._extract_excel,
            '.pdf': self._extract_pdf,
        }
    
    async def extract_content(self, record_id: int) -> Optional[Dict[str, Any]]:
        """
        提取文件内容 - 优先使用已提取的内容
        
        Args:
            record_id: 文件记录ID
            
        Returns:
            包含文件内容的字典，如果提取失败则返回None
        """
        try:
            # 获取文件记录
            file_record = await self.file_upload_service.get_upload_record_by_id(record_id)
            if not file_record:
                self.logger.warning(f"File record not found: {record_id}")
                return None
            
            # 优先使用已提取的内容
            if (file_record.upload_metadata and 
                file_record.upload_metadata.get("extracted_content") and
                file_record.upload_metadata.get("extraction_status") == "success"):
                
                # 使用已提取的内容
                extracted_content = file_record.upload_metadata.get("extracted_content")
                self.logger.info(f"Using pre-extracted content for file {file_record.original_filename}")
                
                return {
                    "id": file_record.id,
                    "original_filename": file_record.original_filename,
                    "file_url": file_record.file_url,
                    "content_type": file_record.content_type,
                    "content": extracted_content,
                    "file_size": file_record.file_size,
                    "file_extension": Path(file_record.original_filename).suffix.lower(),
                    "from_cache": True  # 标记内容来自缓存
                }
            
            # 如果没有已提取的内容，则从文件中提取
            self.logger.info(f"Extracting content from file source for {file_record.original_filename}")
            
            # 检查文件扩展名
            file_ext = Path(file_record.original_filename).suffix.lower()
            if file_ext not in self.supported_extensions:
                self.logger.warning(f"Unsupported file type: {file_ext}")
                return None
            
            # 提取文件内容
            extractor_func = self.supported_extensions[file_ext]
            content = await extractor_func(file_record)
            
            if content is None:
                return None
            
            # 返回结构化的文件信息
            return {
                "id": file_record.id,
                "original_filename": file_record.original_filename,
                "file_url": file_record.file_url,
                "content_type": file_record.content_type,
                "content": content,
                "file_size": file_record.file_size,
                "file_extension": file_ext,
                "from_cache": False  # 标记内容来自文件提取
            }
            
        except Exception as e:
            self.logger.error(f"Failed to extract content from file {record_id}", exception=e)
            return None
    
    async def extract_multiple_contents(self, record_ids: List[int]) -> List[Dict[str, Any]]:
        """
        批量提取多个文件的内容 - 优先使用已提取的内容
        
        Args:
            record_ids: 文件记录ID列表
            
        Returns:
            成功提取的文件内容列表
        """
        if not record_ids:
            return []
        
        # 并发提取多个文件内容
        tasks = [self.extract_content(record_id) for record_id in record_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉失败的结果
        extracted_contents = []
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Failed to extract file content", exception=result)
                continue
            if result is not None:
                extracted_contents.append(result)
        
        return extracted_contents
    
    async def _extract_text(self, file_record) -> Optional[str]:
        """提取文本文件内容"""
        try:
            if file_record.storage_type == "local":
                # 本地文件
                if not file_record.file_path or not os.path.exists(file_record.file_path):
                    self.logger.error(f"Local file not found: {file_record.file_path}")
                    return None
                
                with open(file_record.file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif file_record.storage_type == "tos":
                # TOS存储文件
                if not file_record.object_key:
                    self.logger.error("TOS object key is missing")
                    return None
                
                # 注意：这里需要延迟导入以避免循环导入
                from app.services.storage_service import storage_business_service
                # 使用存储服务下载文件内容
                content = await storage_business_service.download_file_content(file_record.object_key)
                return content
            
            else:
                self.logger.error(f"Unsupported storage type: {file_record.storage_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to extract text from {file_record.original_filename}", exception=e)
            return None
    
    async def _extract_markdown(self, file_record) -> Optional[str]:
        """提取Markdown文件内容"""
        # Markdown文件本质上也是文本文件，使用相同的提取逻辑
        return await self._extract_text(file_record)
    
    async def _extract_word(self, file_record) -> Optional[str]:
        """提取Word文档内容"""
        try:
            if file_record.storage_type == "local":
                # 本地文件
                if not file_record.file_path or not os.path.exists(file_record.file_path):
                    self.logger.error(f"Local Word file not found: {file_record.file_path}")
                    return None
                
                # 直接使用Document类处理本地文件
                doc = Document(file_record.file_path)
                return self._extract_text_from_docx(doc)
            
            elif file_record.storage_type == "tos":
                # TOS存储文件
                if not file_record.object_key:
                    self.logger.error("TOS object key is missing")
                    return None
                
                # 使用存储服务下载二进制文件内容
                from app.services.storage_service import storage_business_service
                file_content = await storage_business_service.download_file_content_binary(file_record.object_key)
                
                if not file_content:
                    self.logger.error(f"Failed to download Word file from TOS: {file_record.object_key}")
                    return None
                
                # 将二进制内容写入临时文件
                with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.docx') as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                try:
                    # 处理临时文件
                    doc = Document(temp_file_path)
                    return self._extract_text_from_docx(doc)
                finally:
                    # 清理临时文件
                    os.unlink(temp_file_path)
            
            else:
                self.logger.error(f"Unsupported storage type: {file_record.storage_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to extract Word content from {file_record.original_filename}", exception=e)
            return None
    
    def _extract_text_from_docx(self, doc) -> Optional[str]:
        """从docx文档对象中提取文本内容"""
        try:
            full_text = []
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    full_text.append(paragraph.text)
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        full_text.append(" | ".join(row_text))
            
            # 合并所有文本
            content = "\n".join(full_text)
            
            # 应用内容长度限制
            if len(content) > 500000:
                self.logger.warning(f"Word document content exceeds 500000 characters, truncating")
                content = content[:500000]
            
            return content.strip() if content.strip() else None
            
        except Exception as e:
            self.logger.error("Failed to extract text from docx document", exception=e)
            return None
    
    async def _extract_excel(self, file_record) -> Optional[str]:
        """提取Excel文件内容"""
        try:
            if file_record.storage_type == "local":
                # 本地文件
                if not file_record.file_path or not os.path.exists(file_record.file_path):
                    self.logger.error(f"Local Excel file not found: {file_record.file_path}")
                    return None
                
                # 直接处理本地文件
                return self._extract_text_from_excel(file_record.file_path)
            
            elif file_record.storage_type == "tos":
                # TOS存储文件
                if not file_record.object_key:
                    self.logger.error("TOS object key is missing")
                    return None
                
                # 使用存储服务下载二进制文件内容
                from app.services.storage_service import storage_business_service
                file_content = await storage_business_service.download_file_content_binary(file_record.object_key)
                
                if not file_content:
                    self.logger.error(f"Failed to download Excel file from TOS: {file_record.object_key}")
                    return None
                
                # 将二进制内容写入临时文件
                with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.xlsx') as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                try:
                    # 处理临时文件
                    return self._extract_text_from_excel(temp_file_path)
                finally:
                    # 清理临时文件
                    os.unlink(temp_file_path)
            
            else:
                self.logger.error(f"Unsupported storage type: {file_record.storage_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to extract Excel content from {file_record.original_filename}", exception=e)
            return None
    
    async def _extract_pdf(self, file_record) -> Optional[str]:
        """提取PDF文件内容"""
        if fitz is None:
            self.logger.error("PyMuPDF (fitz) is not installed. Please install it with: pip install PyMuPDF")
            return None
            
        try:
            if file_record.storage_type == "local":
                # 本地文件
                if not file_record.file_path or not os.path.exists(file_record.file_path):
                    self.logger.error(f"Local PDF file not found: {file_record.file_path}")
                    return None
                
                # 直接处理本地文件
                return self._extract_text_from_pdf(file_record.file_path)
            
            elif file_record.storage_type == "tos":
                # TOS存储文件
                if not file_record.object_key:
                    self.logger.error("TOS object key is missing")
                    return None
                
                # 使用存储服务下载二进制文件内容
                from app.services.storage_service import storage_business_service
                file_content = await storage_business_service.download_file_content_binary(file_record.object_key)
                
                if not file_content:
                    self.logger.error(f"Failed to download PDF file from TOS: {file_record.object_key}")
                    return None
                
                # 将二进制内容写入临时文件
                with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.pdf') as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                try:
                    # 处理临时文件
                    return self._extract_text_from_pdf(temp_file_path)
                finally:
                    # 清理临时文件
                    os.unlink(temp_file_path)
            
            else:
                self.logger.error(f"Unsupported storage type: {file_record.storage_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to extract PDF content from {file_record.original_filename}", exception=e)
            return None
    
    def _extract_text_from_excel(self, file_path: str) -> Optional[str]:
        """从Excel文件中提取文本内容，格式化为大模型友好的格式"""
        try:
            full_text = []
            
            # 使用read_only模式处理大文件
            wb = load_workbook(filename=file_path, data_only=True)
            self.logger.info(f"Processing Excel file with sheets: {wb.sheetnames}")
            
            # 添加Excel文件元数据
            full_text.append("=== Excel 文档内容 ===")
            full_text.append(f"工作表数量: {len(wb.sheetnames)}")
            full_text.append(f"工作表名称: {', '.join(wb.sheetnames)}")
            full_text.append("")
            
            # 处理每个工作表
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                full_text.append(f"## 工作表: {sheet_name}")
                
                # 获取工作表维度信息
                if sheet.max_row > 0 and sheet.max_column > 0:
                    full_text.append(f"数据范围: {sheet.max_row} 行 × {sheet.max_column} 列")
                    
                    # 尝试识别表头（前几行）
                    header_rows = self._identify_header_rows(sheet)
                    data_start_row = header_rows
                    
                    # 提取表格数据
                    table_content = self._format_table_as_markdown(sheet, data_start_row)
                    if table_content:
                        full_text.append(table_content)
                    else:
                        full_text.append("*此工作表无有效数据*")
                else:
                    full_text.append("*此工作表为空*")
                
                full_text.append("")  # 工作表间空行
            
            # 关闭工作簿
            wb.close()
            
            # 合并所有文本
            content = "\n".join(full_text)
            
            # 应用内容长度限制
            if len(content) > 500000:
                self.logger.warning(f"Excel file content exceeds 500000 characters, truncating")
                content = content[:500000]
            
            result = content.strip() if content.strip() else None
            self.logger.info(f"Excel extraction completed. Content length: {len(result) if result else 0}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to extract text from Excel file {file_path}", exception=e)
            return None
    
    def _identify_header_rows(self, sheet) -> int:
        """识别表头行数"""
        max_header_rows = min(3, sheet.max_row)  # 最多检查前3行
        header_row_count = 1  # 默认第一行为表头
        
        for row_idx in range(1, max_header_rows + 1):
            row = sheet[row_idx]
            non_empty_cells = sum(1 for cell in row if cell.value and str(cell.value).strip())
            
            # 如果该行非空单元格数 > 0，且包含可能的标题特征
            if non_empty_cells > 0:
                # 检查是否包含标题特征（如：粗体、字符串等）
                has_header_features = False
                for cell in row:
                    if (cell.value and 
                        isinstance(cell.value, str) and 
                        len(str(cell.value)) > 0):
                        has_header_features = True
                        break
                
                if has_header_features:
                    header_row_count = row_idx
                else:
                    break
            else:
                break
        
        return header_row_count
    
    def _format_table_as_markdown(self, sheet, header_row_count: int) -> str:
        """将表格格式化为Markdown格式"""
        table_lines = []
        
        # 收集所有行数据
        all_rows = []
        for row in sheet.iter_rows(min_row=1, values_only=True):
            row_data = []
            for cell_value in row:
                if cell_value is not None:
                    if isinstance(cell_value, str):
                        processed_value = cell_value.strip()
                    else:
                        processed_value = str(cell_value).strip()
                    row_data.append(processed_value if processed_value else "")
                else:
                    row_data.append("")
            all_rows.append(row_data)
        
        if not all_rows:
            return ""
        
        # 过滤空行
        non_empty_rows = [row for row in all_rows if any(cell.strip() for cell in row)]
        if not non_empty_rows:
            return ""
        
        # 确定最大列数
        max_cols = max(len(row) for row in non_empty_rows)
        
        # 标准化每行的列数
        standardized_rows = []
        for row in non_empty_rows:
            standardized_row = row + [""] * (max_cols - len(row))
            standardized_rows.append(standardized_row)
        
        # 添加表头行
        if header_row_count > 0 and len(standardized_rows) >= header_row_count:
            header_row = standardized_rows[header_row_count - 1]
            table_lines.append("| " + " | ".join(header_row) + " |")
            
            # 添加Markdown分隔线
            separator = ["---"] * max_cols
            table_lines.append("| " + " | ".join(separator) + " |")
            
            # 添加数据行
            for row in standardized_rows[header_row_count:]:
                table_lines.append("| " + " | ".join(row) + " |")
        else:
            # 没有明确的表头，直接作为数据表
            for row in standardized_rows:
                table_lines.append("| " + " | ".join(row) + " |")
        
        return "\n".join(table_lines)
    
    def _extract_text_from_pdf(self, file_path: str) -> Optional[str]:
        """从PDF文件中提取文本内容"""
        if fitz is None:
            self.logger.error("PyMuPDF (fitz) is not installed. Please install it with: pip install PyMuPDF")
            return None
            
        try:
            full_text = []
            
            # 打开PDF文件
            doc = fitz.open(file_path)
            self.logger.info(f"Processing PDF file with {len(doc)} pages")
            
            # 添加PDF文件元数据
            full_text.append("=== PDF 文档内容 ===")
            full_text.append(f"页数: {len(doc)}")
            
            # 提取文档元数据
            metadata = doc.metadata
            if metadata:
                title = metadata.get('title', '未知标题')
                author = metadata.get('author', '未知作者')
                creator = metadata.get('creator', '未知创建工具')
                full_text.append(f"标题: {title}")
                full_text.append(f"作者: {author}")
                full_text.append(f"创建工具: {creator}")
            
            full_text.append("")
            
            # 逐页提取文本内容
            for page_num in range(len(doc)):
                page = doc[page_num]
                # 使用getattr避免类型检查错误
                page_text = getattr(page, 'get_text', lambda: '')()
                
                if page_text.strip():
                    full_text.append(f"## 第 {page_num + 1} 页")
                    full_text.append(page_text)
                    full_text.append("")  # 页间空行
                else:
                    full_text.append(f"## 第 {page_num + 1} 页")
                    full_text.append("*此页无文本内容*")
                    full_text.append("")
            
            # 关闭文档
            doc.close()
            
            # 合并所有文本
            content = "\n".join(full_text)
            
            # 应用内容长度限制
            if len(content) > 500000:
                self.logger.warning(f"PDF file content exceeds 500000 characters, truncating")
                content = content[:500000]
            
            result = content.strip() if content.strip() else None
            self.logger.info(f"PDF extraction completed. Content length: {len(result) if result else 0}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to extract text from PDF file {file_path}", exception=e)
            return None
    
    def is_supported_file(self, filename: str) -> bool:
        """
        检查文件类型是否支持
        
        Args:
            filename: 文件名
            
        Returns:
            是否支持该文件类型
        """
        file_ext = Path(filename).suffix.lower()
        return file_ext in self.supported_extensions
    
    def get_supported_extensions(self) -> List[str]:
        """
        获取支持的文件扩展名列表
        
        Returns:
            支持的文件扩展名列表
        """
        return list(self.supported_extensions.keys())


# 全局文件提取器实例
file_extractor = FileExtractor()
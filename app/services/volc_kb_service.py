"""
火山知识库业务服务模块

处理与火山引擎知识库相关的业务逻辑。
"""

import mimetypes
from typing import Optional, Dict, Any, List
from fastapi import HTTPException

from app.logger.logger import get_logger
from app.clients import VolcKnowledgeBaseClient
from app.auth import schemas as auth_schemas
from app.clients.schemas import DocumentUploadBusinessResponse
# 从存储路由器导入全局存储业务服务实例
from app.services.storage_service import storage_business_service
# 导入数据库服务
from app.knowledge_base.services import document_service, knowledge_base_service

volc_kb_logger = get_logger("volc.kb.service")


class VolcKnowledgeBaseService:
    """火山知识库业务服务类，处理与火山引擎知识库相关的业务逻辑"""
    
    def __init__(self):
        """
        初始化火山知识库业务服务
        """
        self.volc_kb_client = VolcKnowledgeBaseClient()
        self.logger = volc_kb_logger
    
    
    async def upload_document_with_business_logic(
        self,
        kb_id: int,
        volc_kb_id: str,
        file_path: str,
        filename: str,
        metadata: Optional[Dict[str, Any]],
        current_user: auth_schemas.User
    ) -> DocumentUploadBusinessResponse:
        """
        上传文档并处理相关业务逻辑
        
        Args:
            kb_id: 数据库知识库ID
            volc_kb_id: 火山引擎知识库ID
            file_path: 文件路径
            filename: 文件名
            metadata: 文档元数据
            current_user: 当前用户信息
            
        Returns:
            DocumentUploadBusinessResponse: 文档上传结果
        """
        try:
            
            # 检查文件大小，防止大文件导致内存溢出
            import os
            MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
            file_size = os.path.getsize(file_path)
            if file_size > MAX_FILE_SIZE:
                raise HTTPException(status_code=413, detail="文件过大，请上传小于50MB的文件")
            
            # 先上传文件到存储服务
            with open(file_path, 'rb') as file:
                file_content = file.read()
            
            # 上传到存储服务
            upload_result = await storage_business_service.upload_file_with_business_logic(
                file_content=file_content,
                filename=filename,
                folder="knowledge_base",  # 存储到知识库文件夹
                content_type=None,  # 让系统自动检测
                current_user=current_user
            )
            
            # 使用存储服务返回的URL上传到火山知识库
            # 确保file_url不为None
            if not upload_result.file_url:
                raise HTTPException(status_code=500, detail="文件上传到存储服务后未返回有效的URL")
                
            result = await self.volc_kb_client.upload_document_by_url(
                resource_id=volc_kb_id,
                file_url=upload_result.file_url,
                filename=filename,
                metadata=metadata
            )
            
            if result.success:
                # 文档成功上传到火山知识库后，将其信息持久化到数据库
                try:
                    # 创建文档记录
                    # 自动检测文件的MIME类型
                    content_type = mimetypes.guess_type(filename)[0] or "application/octet-stream"
                    
                    # 重新读取文件内容用于数据库存储
                    with open(file_path, 'rb') as file:
                        file_content = file.read()
                    
                    document_record = await document_service.add_document(
                        kb_id=kb_id,  # 使用数据库知识库ID
                        doc_id=result.doc_id or '',
                        file_path=upload_result.file_url,
                        filename=filename,
                        file_content=file_content,
                        content_type=content_type,
                        metadata={
                            **(metadata or {}),
                        }
                    )
                    
                    self.logger.info(f"文档信息已持久化到数据库: {document_record.doc_id}")
                    return DocumentUploadBusinessResponse.success_response(
                        volc_response=result,
                        db_doc_id=document_record.doc_id
                    )
                    
                except Exception as db_error:
                    self.logger.error(f"文档信息持久化到数据库失败: {str(db_error)}")
                    return DocumentUploadBusinessResponse.error_response(str(db_error))
            else:
                error_msg = result.error or "Unknown error"
                self.logger.error(f"文档上传到知识库失败: {error_msg}")
                return DocumentUploadBusinessResponse.error_response(f"文档上传到知识库失败: {error_msg}")
                
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"上传文档时出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"上传文档时出错: {str(e)}")
    
    async def search_documents_with_business_logic(
        self,
        kb_id: int,
        query: str,
        limit: int,
        current_user: auth_schemas.User
    ) -> List[Dict[str, Any]]:
        """
        搜索文档并处理相关业务逻辑
        
        Args:
            kb_id: 知识库ID
            query: 搜索查询
            limit: 返回结果数量限制
            current_user: 当前用户信息
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            # 检查知识库是否存在且有效
            knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
            if not knowledge_base:
                raise HTTPException(status_code=404, detail="知识库不存在或已被删除")
            
            # 使用relation_kb_id作为火山引擎知识库ID
            volc_kb_id = knowledge_base.relation_kb_id
            
            # 搜索文档
            results = await self.volc_kb_client.search_documents(
                kb_id=volc_kb_id,
                query=query,
                limit=limit
            )
            
            self.logger.info(f"文档搜索成功: {query}")
            return results
                
        except Exception as e:
            self.logger.error(f"搜索文档时出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"搜索文档时出错: {str(e)}")
    
    async def delete_document_with_business_logic(
        self,
        kb_id: int,
        doc_id: str,
        current_user: auth_schemas.User
    ) -> bool:
        """
        删除文档并处理相关业务逻辑
        
        Args:
            kb_id: 知识库ID
            doc_id: 文档ID
            current_user: 当前用户信息
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查知识库是否存在且有效
            knowledge_base = await knowledge_base_service.get_knowledge_base(kb_id)
            if not knowledge_base:
                raise HTTPException(status_code=404, detail="知识库不存在或已被删除")
            
            # 使用relation_kb_id作为火山引擎知识库ID
            volc_kb_id = knowledge_base.relation_kb_id
            
            # 删除文档
            success = await self.volc_kb_client.delete_document(
                kb_id=volc_kb_id,
                doc_id=doc_id
            )
            
            if success:
                self.logger.info(f"文档删除成功: {doc_id}")
                return True
            else:
                self.logger.error(f"文档删除失败: {doc_id}")
                raise HTTPException(status_code=500, detail=f"文档删除失败: {doc_id}")
                
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"删除文档时出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除文档时出错: {str(e)}")
    
    async def get_knowledge_base_info_with_business_logic(
        self,
        resource_id: str,
    ) -> Dict[str, Any]:
        """
        获取知识库信息并处理相关业务逻辑
        
        Args:
            kb_id: 知识库ID
            current_user: 当前用户信息
            
        Returns:
            Dict: 知识库信息
        """
        try:
            # 获取知识库信息
            collection = await self.volc_kb_client.get_knowledge_base_info(resource_id)
            
            # 将Collection对象转换为Dict[str, Any]
            info = {
                "id": collection.resource_id,
                "name": collection.collection_name,
                "description": collection.description,
                "doc_num":collection.doc_num,
                "project":collection.project,
                "update_time": collection.update_time
            }
            
            self.logger.info(f"获取知识库信息成功: {resource_id}")
            return info
                
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"获取知识库信息时出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取知识库信息时出错: {str(e)}")

"""
存储服务模块

处理文件上传、下载等操作的业务逻辑。
"""

import io
from typing import Optional
from fastapi import HTTPException

from app.logger.logger import get_logger
from app.auth import schemas as auth_schemas
from app.clients import StorageClient
from app.clients.models import UploadResult, FileInfo, FileUploadResponse
from app.database.models import FileUploadRecord
from app.database.enums import UploadStatus
from app.dependencies import get_file_upload_service
from app.services.file_extractor import file_extractor

storage_logger = get_logger("storage.service")

class StorageBusinessService:
    """存储业务服务类，处理文件操作的业务逻辑"""

    def __init__(self, storage_client: StorageClient):
        """
        初始化存储业务服务
        
        Args:
            storage_client: 存储客户端实例
        """
        self.storage_client = storage_client
        self.file_upload_service = get_file_upload_service()
        self.logger = storage_logger

    async def upload_file_with_business_logic(
        self,
        file_content: bytes,
        filename: str,
        folder: Optional[str],
        content_type: Optional[str],
        current_user: auth_schemas.User
    ) -> FileUploadResponse:
        """
        上传文件并处理相关业务逻辑
        
        Args:
            file_content: 文件内容
            filename: 文件名
            folder: 存储文件夹路径（可选）
            content_type: 文件MIME类型（可选）
            current_user: 当前用户信息
            
        Returns:
            FileUploadResponse: 上传响应，基于FileUploadRecord的安全响应
        """
        upload_record = None
        try:
            if not filename:
                raise HTTPException(status_code=400, detail="文件名不能为空")
            
            # 创建上传记录
            upload_record = await self.file_upload_service.create_upload_record(
                original_filename=filename,
                file_size=0,  # 文件大小将在上传后更新
                content_type=content_type,
                storage_type="tos",
                upload_status=UploadStatus.PENDING.value,
                uploader_id=current_user.id,
                uploader_name=current_user.username or "匿名",
                upload_metadata=None
            )
            
            # 创建文件流
            file_stream = io.BytesIO(file_content)
            
            # 更新文件大小
            if upload_record:
                await self.file_upload_service.update_upload_record(
                    record_id=upload_record.id,
                    file_size=len(file_content)
                )
            
            # 上传文件
            # 使用YYYYMMDD格式的日期作为文件夹名的一部分
            from datetime import datetime
            date_str = datetime.now().strftime("%Y%m%d")
            folder_path = f"business/pdo_agent/{date_str}"
            if folder:
                folder_path = f"{folder_path}/{folder}"
                
            result = await self.storage_client.upload_file(
                file_data=file_stream,
                filename=filename,
                folder=folder_path,
                content_type=content_type
            )
            
            # 更新上传记录为成功状态
            if upload_record:
                updated_record = await self.file_upload_service.update_upload_record(
                    record_id=upload_record.id,
                    upload_status=UploadStatus.SUCCESS.value,
                    object_key=result.object_key,
                    file_url=result.file_url,
                    file_size=result.file_size
                )
                self.logger.info(f"文件上传成功: {filename}")
                
                # 创建并返回FileUploadResponse
                if updated_record:
                    return FileUploadResponse(
                        id=updated_record.id,
                        original_filename=updated_record.original_filename,
                        file_size=updated_record.file_size,
                        content_type=updated_record.content_type,
                        storage_type=updated_record.storage_type,
                        object_key=updated_record.object_key,
                        file_url=updated_record.file_url,
                        upload_status=updated_record.upload_status,
                        error_message=updated_record.error_message,
                        uploader_name=updated_record.uploader_name,
                        file_extension=updated_record.file_extension,
                        readable_size=updated_record.readable_size,
                        created_at=updated_record.created_at,
                        updated_at=updated_record.updated_at
                    )
            
            # 如果没有上传记录或更新失败，抛出异常
            raise HTTPException(status_code=500, detail="无法创建上传响应")
            
        except HTTPException:
            # 如果有上传记录，更新为失败状态
            if upload_record:
                await self.file_upload_service.update_upload_record(
                    record_id=upload_record.id,
                    upload_status=UploadStatus.FAILED.value,
                    error_message="HTTP异常"
                )
            raise
        except Exception as e:
            # 如果有上传记录，更新为失败状态
            if upload_record:
                await self.file_upload_service.update_upload_record(
                    record_id=upload_record.id,
                    upload_status=UploadStatus.FAILED.value,
                    error_message=str(e)
                )
            self.logger.error(f"上传文件时出错: {str(e)}", e)
            raise HTTPException(status_code=500, detail=f"上传文件时出错: {str(e)}")

    async def download_file_with_business_logic(self, object_key: str) -> bytes:
        """
        下载文件并处理相关业务逻辑
        
        Args:
            object_key: 文件的对象键
            
        Returns:
            tuple[bytes, FileInfo]: 文件内容和文件信息
        """
        try:
            
            # 下载文件内容
            file_content = await self.storage_client.download_file(object_key)
            if file_content is None:
                raise HTTPException(status_code=404, detail=f"无法下载文件: {object_key}")
            
            self.logger.info(f"文件下载成功: {object_key}")
            return file_content
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"下载文件时出错: {str(e)}", e)
            raise HTTPException(status_code=500, detail=f"下载文件时出错: {str(e)}")

    async def download_file_content(self, object_key: str) -> Optional[str]:
        """
        下载文件内容并转换为字符串
        
        Args:
            object_key: 文件的对象键
            
        Returns:
            文件内容的字符串表示，如果失败则返回None
        """
        try:
            # 下载文件内容
            file_content = await self.storage_client.download_file(object_key)
            if file_content is None:
                self.logger.error(f"Failed to download file: {object_key}")
                return None
            
            # 尝试解码为UTF-8文本
            try:
                return file_content.decode('utf-8')
            except UnicodeDecodeError as e:
                self.logger.warning(f"Failed to decode file {object_key} as UTF-8", e)
                return None
            
        except Exception as e:
            self.logger.error(f"Failed to download file content: {object_key}", exception=e)
            return None

    async def download_file_content_binary(self, object_key: str) -> Optional[bytes]:
        """
        下载文件内容并返回二进制数据
        
        Args:
            object_key: 文件的对象键
            
        Returns:
            文件内容的二进制数据，如果失败则返回None
        """
        try:
            # 下载文件内容
            file_content = await self.storage_client.download_file(object_key)
            if file_content is None:
                self.logger.error(f"Failed to download file: {object_key}")
                return None
            
            return file_content
            
        except Exception as e:
            self.logger.error(f"Failed to download binary file content: {object_key}", exception=e)
            return None

    async def extract_file_content_async(self, record_id: int) -> bool:
        """
        异步提取文件内容
        
        Args:
            record_id: 文件记录ID
            
        Returns:
            是否成功启动提取任务
        """
        try:
            # 更新状态为提取中
            file_upload_service = get_file_upload_service()
            await file_upload_service.update_extraction_status(
                record_id=record_id,
                status=UploadStatus.EXTRACTING.value
            )
            
            # 异步执行提取任务
            import asyncio
            asyncio.create_task(self._perform_extraction(record_id))
            
            self.logger.info(f"Started async extraction for file record {record_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start extraction for file {record_id}", exception=e)
            return False
    
    async def _perform_extraction(self, record_id: int):
        """
        执行文件内容提取的实际任务
        
        Args:
            record_id: 文件记录ID
        """
        file_upload_service = get_file_upload_service()
        
        try:
            # 提取文件内容
            extracted_content = await file_extractor.extract_content(record_id)
            
            if extracted_content is None:
                # 提取失败
                await file_upload_service.update_extraction_status(
                    record_id=record_id,
                    status=UploadStatus.FAILED.value,
                    error_message="文件内容提取失败：不支持的文件类型或文件损坏"
                )
                self.logger.error(f"Failed to extract content from file {record_id}")
                return
            
            # 检查文本内容长度
            content_text = extracted_content["content"]
            if content_text and len(content_text) > 33000:
                await file_upload_service.update_extraction_status(
                    record_id=record_id,
                    status=UploadStatus.FAILED.value,
                    error_message="文件内容提取失败：文本内容超过30000字限制"
                )
                self.logger.error(f"File content too long for record {record_id}: {len(content_text)} characters")
                return
            
            # 提取成功
            await file_upload_service.update_extraction_status(
                record_id=record_id,
                status=UploadStatus.SUCCESS.value,
                extracted_content=extracted_content["content"]
            )
            
            self.logger.info(f"Successfully extracted content from file {record_id}: {extracted_content['original_filename']}")
            
        except Exception as e:
            # 提取过程中出现异常
            await file_upload_service.update_extraction_status(
                record_id=record_id,
                status=UploadStatus.FAILED.value,
                error_message=f"文件提取过程中出现错误: {str(e)}"
            )
            self.logger.error(f"Extraction failed for file {record_id}", exception=e)

    async def download_file_by_record_id_with_business_logic(self, record_id: int) -> tuple[bytes, FileUploadRecord]:
        """
        通过上传记录ID下载文件并处理相关业务逻辑
        
        Args:
            record_id: 上传记录ID
            
        Returns:
            tuple[bytes, FileInfo, FileUploadRecord]: 文件内容、文件信息和上传记录
        """
        try:
            # 获取上传记录（排除已删除的记录）
            upload_record = await self.file_upload_service.get_upload_record_by_id(record_id, include_deleted=False)
            if not upload_record:
                raise HTTPException(status_code=404, detail=f"上传记录不存在或已被删除: {record_id}")
            
            # 检查上传状态
            if UploadStatus.from_string(upload_record.upload_status) != UploadStatus.SUCCESS:
                raise HTTPException(status_code=400, detail=f"文件上传未完成或失败，状态: {upload_record.upload_status}")
            
            # 检查object_key是否存在
            if not upload_record.object_key:
                raise HTTPException(status_code=400, detail="文件对象键不存在")
            
            # 使用object_key下载文件
            file_content = await self.download_file_with_business_logic(upload_record.object_key)
            
            self.logger.info(f"通过记录ID下载文件成功: record_id={record_id}, object_key={upload_record.object_key}")
            return file_content, upload_record
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"通过记录ID下载文件时出错: {str(e)}", e)
            raise HTTPException(status_code=500, detail=f"通过记录ID下载文件时出错: {str(e)}")

    async def delete_file_with_business_logic(self, object_key: str) -> bool:
        """
        删除文件并处理相关业务逻辑
        
        Args:
            object_key: 文件的对象键
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查文件是否存在
            file_info = await self.storage_client.get_file_info(object_key)
            if not file_info:
                raise HTTPException(status_code=404, detail=f"文件不存在: {object_key}")
            
            # 删除文件
            success = await self.storage_client.delete_file(object_key)
            
            if success:
                self.logger.info(f"文件删除成功: {object_key}")
                return True
            else:
                raise HTTPException(status_code=500, detail=f"删除文件失败: {object_key}")
                
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"删除文件时出错: {str(e)}", e)
            raise HTTPException(status_code=500, detail=f"删除文件时出错: {str(e)}")


    async def get_file_info_by_record_id_with_business_logic(self, record_id: int) -> FileUploadResponse:
        """
        通过上传记录ID获取文件信息并处理相关业务逻辑
        
        Args:
            record_id: 上传记录ID
            
        Returns:
            FileUploadResponse: 文件上传响应，包含上传记录的详细信息
        """
        try:
            # 获取上传记录（排除已删除的记录）
            upload_record = await self.file_upload_service.get_upload_record_by_id(record_id, include_deleted=False)
            if not upload_record:
                raise HTTPException(status_code=404, detail=f"上传记录不存在或已被删除: {record_id}")
            
            # 检查上传状态
            if UploadStatus.from_string(upload_record.upload_status) != UploadStatus.SUCCESS:
                raise HTTPException(status_code=400, detail=f"文件上传未完成或失败，状态: {upload_record.upload_status}")
            
            # 检查object_key是否存在
            if not upload_record.object_key:
                raise HTTPException(status_code=400, detail="文件对象键不存在")
            
            # 创建并返回FileUploadResponse
            response = FileUploadResponse(
                id=upload_record.id,
                original_filename=upload_record.original_filename,
                file_size=upload_record.file_size,
                content_type=upload_record.content_type,
                storage_type=upload_record.storage_type,
                object_key=upload_record.object_key,
                file_url=upload_record.file_url,
                upload_status=upload_record.upload_status,
                error_message=upload_record.error_message,
                uploader_name=upload_record.uploader_name,
                file_extension=upload_record.file_extension,
                readable_size=upload_record.readable_size,
                created_at=upload_record.created_at,
                updated_at=upload_record.updated_at
            )
            
            self.logger.info(f"通过记录ID获取文件信息成功: record_id={record_id}, object_key={upload_record.object_key}")
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"通过记录ID获取文件信息时出错: {str(e)}", e)
            raise HTTPException(status_code=500, detail=f"通过记录ID获取文件信息时出错: {str(e)}")

    async def delete_file_record_with_business_logic(self, record_id: int, current_user) -> dict:
        """
        删除文件记录的业务逻辑（逻辑删除）
        
        Args:
            record_id: 上传记录ID
            current_user: 当前用户信息
            
        Returns:
            dict: 删除操作结果
        """
        try:
            # 获取上传记录（排除已删除的记录）
            upload_record = await self.file_upload_service.get_upload_record_by_id(record_id, include_deleted=False)
            if not upload_record:
                self.logger.warning(f"尝试删除不存在或已删除的文件记录: record_id={record_id}, user_id={current_user.id}")
                raise HTTPException(status_code=404, detail=f"文件记录不存在或已被删除: {record_id}")
            
            # 权限检查：用户只能删除自己上传的文件
            if upload_record.uploader_id != current_user.id:
                self.logger.warning(
                    f"用户尝试删除他人文件: record_id={record_id}, "
                    f"owner_id={upload_record.uploader_id}, requester_id={current_user.id}"
                )
                raise HTTPException(status_code=403, detail="权限不足：您只能删除自己上传的文件")
            
            # 执行逻辑删除
            deleted_record = await self.file_upload_service.soft_delete_upload_record(record_id)
            if not deleted_record:
                # 这种情况理论上不应该发生，因为我们已经检查过记录存在
                self.logger.error(f"逻辑删除失败: record_id={record_id}")
                raise HTTPException(status_code=500, detail="删除操作失败")
            
            # 记录审计日志
            self.logger.info(
                f"文件记录逻辑删除成功: record_id={record_id}, "
                f"filename={upload_record.original_filename}, "
                f"user_id={current_user.id}, "
                f"object_key={upload_record.object_key}"
            )
            
            return {
                "success": True,
                "message": f"文件 '{upload_record.original_filename}' 已成功删除",
                "record_id": record_id,
                "filename": upload_record.original_filename,
                "deleted_at": deleted_record.updated_at.isoformat()
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(
                f"删除文件记录时出错: record_id={record_id}, user_id={current_user.id}, error={str(e)}", 
                e
            )
            raise HTTPException(status_code=500, detail=f"删除文件记录时出错: {str(e)}")
        
def _initialize_global_storage_business_service():
    """初始化全局存储业务服务实例"""
    try:
        # 获取存储客户端实例
        from app.config.config import get_config
        from app.clients.models import StorageConfig
        from app.clients import StorageClient
        
        config = get_config()
        storage_config = StorageConfig(
            access_key=config.storage.access_key,
            secret_key=config.storage.secret_key,
            endpoint=config.storage.endpoint,
            region=config.storage.region,
            bucket_name=config.storage.bucket_name,
            domain=config.storage.domain
        )
        storage_client = StorageClient(storage_config)
        
        # 延迟导入StorageBusinessService以避免循环导入
        from app.services.storage_service import StorageBusinessService
        return StorageBusinessService(storage_client)
    except Exception as e:
        storage_logger.error(f"初始化全局存储业务服务失败: {str(e)}", e)
        raise
    
# 全局存储业务服务实例 - 直接初始化
try:
    storage_business_service = _initialize_global_storage_business_service()
except Exception as e:
    storage_logger.error(f"初始化全局存储业务服务失败: {str(e)}", e)
    storage_business_service = None
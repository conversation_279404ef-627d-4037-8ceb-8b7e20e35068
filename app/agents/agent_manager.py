"""
Agent Manager for managing multiple agents and their lifecycle.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from llama_index.core.tools import BaseTool
from llama_index.core.llms import LLM
from llama_index.core.memory import BaseMemory
from llama_index.core.agent.workflow import AgentStream, AgentOutput, ReActAgent, FunctionAgent, BaseWorkflowAgent, AgentInput

from app.agents.models import LLMConfig
from app.mcp.models import MCPServerConfig
from app.agents.structured_output_models import StructuredOutputConfig
from app.agents.dynamic_model_generator import DynamicModelGenerator
from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import AgentService, ChatService
from app.llm import get_llm_manager
from llama_index.core.workflow import Context
from app.mcp.mcp_manager import mcp_manager
from llama_index.core import PromptTemplate
from llama_index.core.agent.react.formatter import ReActChatFormatter
from llama_index.core.memory import Memory
from llama_index.core.base.llms.types import ChatMessage

class AgentManager:
    """
    Manager class for handling multiple agents.

    This class provides functionality to create, configure, and manage
    multiple agents of different types, allowing for easy switching
    between agents and coordinated multi-agent workflows.
    """

    # Registry of available agent types
    AGENT_TYPES: Dict[str, str] = {
        "react": "react"
    }

    def __init__(self):
        """Initialize the agent manager."""
        self._default_agent_id: Optional[str] = None
        self._global_tools: List[BaseTool] = []
        self._global_llm: Optional[LLM] = None
        self._global_memory: Optional[BaseMemory] = None

        # Initialize LLM manager
        self._llm_manager = get_llm_manager()

        # Initialize dynamic model generator
        self._model_generator = DynamicModelGenerator()

        # Initialize logger
        self.logger = get_logger("agents.AgentManager")
        self.logger.info("🚀 AgentManager initialized")

        # Initialize MCP manager reference
        self._mcp_manager = mcp_manager

    def set_global_llm(self, llm: LLM) -> None:
        """
        Set a global LLM to be used by all agents by default.
        
        Args:
            llm: The language model to use globally
        """
        self._global_llm = llm

    def set_global_memory(self, memory: BaseMemory) -> None:
        """
        Set a global memory system to be used by all agents by default.

        Args:
            memory: The memory system to use globally
        """
        self._global_memory = memory

    def get_llm_manager(self):
        """Get the LLM manager instance."""
        return self._llm_manager

    async def list_llm_providers(self) -> List[str]:
        """List all available LLM providers."""
        return await self._llm_manager.list_providers()

    async def get_llm_provider_info(self, provider_id: str) -> Optional[LLM]:
        """Get information about a specific LLM provider."""
        return await self._llm_manager.get_provider_info(provider_id)

    def add_global_tool(self, tool: BaseTool) -> None:
        """
        Add a tool that will be available to all agents.

        Note: LlamaIndex agents don't support dynamic tool addition.
        Tools must be provided at agent creation time.

        Args:
            tool: The tool to add globally
        """
        try:
            tool_name = getattr(tool.metadata, 'name', 'unknown') if hasattr(tool, 'metadata') else 'unknown'
            self._global_tools.append(tool)

            self.logger.log_tool_operation("added globally", tool_name)
            self.logger.warning("Note: Existing agents won't have this tool. Recreate agents to include new tools.")

        except Exception as e:
            self.logger.error("Failed to add global tool", exception=e)
            raise

    def remove_global_tool(self, tool_name: str) -> bool:
        """
        Remove a global tool by name.

        Note: LlamaIndex agents don't support dynamic tool removal.
        This only removes from the global tools list for future agents.

        Args:
            tool_name: Name of the tool to remove

        Returns:
            bool: True if tool was removed, False if not found
        """
        try:
            for i, tool in enumerate(self._global_tools):
                if tool.metadata.name == tool_name:
                    self._global_tools.pop(i)

                    self.logger.log_tool_operation("removed globally", tool_name)
                    self.logger.warning("Note: Existing agents still have this tool. Recreate agents to remove tools.")
                    return True

            self.logger.warning(f"Global tool '{tool_name}' not found for removal")
            return False

        except Exception as e:
            self.logger.error(f"Failed to remove global tool '{tool_name}'", exception=e)
            raise

    async def create_agent(
        self,
        agent_id: str,
        agent_type: str = "react",
        name: Optional[str] = None,
        description: Optional[str] = None,
        tools: Optional[List[BaseTool]] = None,
        memory: Optional[BaseMemory] = None,
        system_prompt: Optional[str] = None,
        set_as_default: bool = False,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        llm_config: Optional[LLMConfig] = None,
        mcp_config: Optional[List[MCPServerConfig]] = None,
        structured_output_config: Optional[StructuredOutputConfig] = None,
        **kwargs
    ) -> Union[ReActAgent, FunctionAgent]:
        """
        Create a new agent.

        Args:
            agent_id: Unique identifier for the agent
            agent_type: Type of agent to create ("react" or "openai")
            name: Agent name (defaults to agent_id)
            description: Agent description
            tools: List of tools (global tools are always included)
            memory: Memory system (uses global memory if not provided)
            system_prompt: System prompt for the agent
            set_as_default: Whether to set this as the default agent
            provider: LLM provider to use
            model: Model name to use
            llm_config: LLM configuration
            mcp_config: List of MCP server configurations to use with this agent
            **kwargs: Additional configuration parameters

        Returns:
            ReActAgent: The created agent

        Raises:
            ValueError: If agent_id already exists or agent_type is invalid
        """
        try:
            self.logger.info(f"🔄 Creating agent '{agent_id}' of type '{agent_type}'")

            # Check if agent already exists in the database
            existing_agent = await AgentService.get_agent_by_id(agent_id)
            if existing_agent:
                error_msg = f"Agent with ID '{agent_id}' already exists in the database"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            if agent_type not in self.AGENT_TYPES:
                error_msg = f"Unknown agent type '{agent_type}'. Available types: {list(self.AGENT_TYPES.keys())}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Use defaults if not provided
            if name is None:
                name = agent_id
            if description is None:
                description = f"Agent {agent_id}"
                
            # Create LLM instance using LLMManager
            if self._global_llm and not provider:
                llm = self._global_llm
            else:
                # If a provider is specified, it's used. If not, the default provider is used.
                # If a model is specified, it's used. If not, the provider's default model is used.
                try:
                    llm, actual_provider, actual_model = await self._llm_manager.create_llm(provider_id=provider, model=model)
                    if not self._global_llm:
                        self.set_global_llm(llm)
                except Exception as e:
                    self.logger.error(f"Failed to create LLM from LLMManager: {e}")
                    raise ValueError("Could not create a valid LLM instance.") from e

            if memory is None:
                memory = self._global_memory

            # Combine global tools with agent-specific tools and MCP tools
            all_tools = self._global_tools.copy()
            if tools:
                all_tools.extend(tools)

            # Add MCP tools
            mcp_tools = self._mcp_manager.get_all_tools()
            all_tools.extend(mcp_tools)

            self.logger.debug(f"Agent '{agent_id}' will have {len(all_tools)} tools")

            # Handle structured output configuration
            if structured_output_config and structured_output_config.enabled:
                try:
                    # Validate and generate dynamic model
                    if structured_output_config.schema_definition:
                        validation_errors = self._model_generator.validate_schema(structured_output_config.schema_definition)
                        if validation_errors:
                            self.logger.warning(f"Structured output schema validation failed: {validation_errors}")
                            # Don't fail agent creation, but disable structured output
                            structured_output_config.enabled = False
                        else:
                            # Generate dynamic model for validation
                            dynamic_model = self._model_generator.generate_model(structured_output_config)
                            if dynamic_model:
                                self.logger.info(f"✅ Successfully generated dynamic model for agent '{agent_id}'")
                            else:
                                self.logger.warning(f"Failed to generate dynamic model for agent '{agent_id}'")
                                structured_output_config.enabled = False
                except Exception as e:
                    self.logger.error(f"Error processing structured output configuration: {e}")
                    structured_output_config.enabled = False

            # Create the agent
            with PerformanceLogger(f"create_agent_{agent_id}", self.logger):
                if agent_type == "react":
                    agent = ReActAgent(
                        name=name,
                        description=description,
                        llm=llm,
                        tools=all_tools,
                        system_prompt=system_prompt,
                        **kwargs
                    )
                else:
                    raise ValueError(f"Unknown agent type: {agent_type}")

            # Save to database first
            try:
                # Save agent to database (this is already in async context)
                await AgentService.create_agent(
                    agent_id=agent_id,
                    name=name,
                    agent_type=agent_type,
                    description=description,
                    system_prompt=system_prompt,
                    model=actual_model,
                    provider=actual_provider,
                    is_default=set_as_default,
                    tools=[tool.metadata.name for tool in all_tools if hasattr(tool, 'metadata') and tool.metadata.name],
                    config=kwargs,
                    llm_config=llm_config,
                    mcp_config=[config.model_dump() for config in mcp_config] if mcp_config else [],
                    structured_output_config=structured_output_config.model_dump() if structured_output_config else None
                )
                self.logger.info(f"💾 Agent '{agent_id}' saved to database")
            except Exception as e:
                self.logger.error(f"Failed to save agent to database: {e}")
                raise ValueError(f"无法将智能体保存到数据库: {str(e)}") from e

            # Set as default if requested (after saving to database)
            if set_as_default:
                try:
                    await AgentService.set_default_agent(agent_id)
                    self.logger.info(f"🎯 Agent '{agent_id}' set as default")
                except Exception as e:
                    self.logger.warning(f"Failed to set agent as default: {e}")
                    # Don't fail the entire operation if setting default fails

            self.logger.log_agent_operation("created", agent_id, f"Type: {agent_type}, Tools: {len(all_tools)}")
            return agent

        except Exception as e:
            self.logger.error(f"Failed to create agent '{agent_id}'", exception=e)
            raise

    async def get_agent(self, agent_id: str, **kwargs) -> Optional[BaseWorkflowAgent]:
        """
        Get an agent by ID.

        Args:
            agent_id: The agent ID

        Returns:
            Union[ReActAgent, FunctionAgent]: The agent, or None if not found
        """
        # Load from database and recreate
        db_agent = await AgentService.get_agent_by_id(agent_id)
        if not db_agent:
            return None

        # Recreate the agent from database configuration
        try:
            # Get LLM configuration
            db_agent_dict = db_agent.to_dict()
            provider_id = db_agent_dict.get("provider")
            model_name = db_agent_dict.get("model")

            if not provider_id:
                self.logger.error(f"Agent '{agent_id}' from database has no provider specified.")
                return None

            llm, actual_provider, actual_model = await self._llm_manager.create_llm(provider_id=provider_id, model=model_name)

            if not llm:
                self.logger.error(f"No LLM available for agent '{agent_id}'")
                return None

            # Recreate the agent
            config = db_agent_dict.get("config", {})
            if not isinstance(config, dict):
                config = {}
            
            # Handle structured output configuration
            structured_output_config = db_agent_dict.get("structured_output_config", {})
            if structured_output_config and isinstance(structured_output_config, dict):
                try:
                    # Reconstruct StructuredOutputConfig from dict
                    from app.agents.structured_output_models import StructuredOutputConfig
                    parsed_config = StructuredOutputConfig(**structured_output_config)
                    
                    if parsed_config.enabled and parsed_config.schema_definition:
                        # Validate and generate dynamic model
                        validation_errors = self._model_generator.validate_schema(parsed_config.schema_definition)
                        if validation_errors:
                            self.logger.warning(f"Structured output schema validation failed for agent '{agent_id}': {validation_errors}")
                            parsed_config.enabled = False
                        else:
                            dynamic_model = self._model_generator.generate_model(parsed_config)
                            if dynamic_model:
                                self.logger.info(f"✅ Successfully loaded structured output config for agent '{agent_id}'")
                                # Store dynamic model in agent config for later use
                                config["_dynamic_model"] = dynamic_model
                            else:
                                self.logger.warning(f"Failed to generate dynamic model for agent '{agent_id}'")
                                parsed_config.enabled = False
                except Exception as e:
                    self.logger.error(f"Error loading structured output config for agent '{agent_id}': {e}")
            else:
                structured_output_config = None
            
            # Get agent-specific MCP tools based on mcp_config
            agent_tools = self._global_tools.copy()
            mcp_config = db_agent_dict.get("mcp_config", [])
            
            if mcp_config and isinstance(mcp_config, list):
                # Get tools from specific MCP servers configured for this agent
                for mcp_server_config in mcp_config:
                    if isinstance(mcp_server_config, dict):
                        server_id = mcp_server_config.get("server_id")
                        if server_id:
                            server_tools = self._mcp_manager.get_server_tools(server_id)
                            agent_tools.extend(server_tools)
                            self.logger.debug(f"Added {len(server_tools)} tools from MCP server '{server_id}' for agent '{agent_id}'")
                            
            raw_system_prompt = db_agent_dict.get('system_prompt') or ""
            system_prompt = PromptTemplate(str(raw_system_prompt))
            system_prompt_str = system_prompt.format(**(kwargs or {}))

            default_fmt = ReActChatFormatter.from_defaults()
            fmt = ReActChatFormatter.from_defaults(
                system_header=(f"{default_fmt.system_header}\n\n"
                               f"## global condition \n\n current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n ## external prompt: \n\n {system_prompt_str}\n\n")
                )
            agent = ReActAgent(
                name=str(db_agent_dict.get("name")),
                description=str(db_agent_dict.get("description", "")),
                llm=llm,
                tools=agent_tools,
                formatter=fmt,
                **config
            )
            agent.get_prompts()

            return agent

        except Exception as e:
            self.logger.error(f"Failed to recreate agent '{agent_id}' from database", exception=e)
            return None

    async def get_default_agent(self) -> Optional[BaseWorkflowAgent]:
        """
        Get the default agent.

        Returns:
            Union[ReActAgent, FunctionAgent]: The default agent, or None if no default is set
        """
        db_agent = await AgentService.get_default_agent()
        if db_agent:
            return await self.get_agent(str(db_agent.agent_id))
        return None

    async def set_default_agent(self, agent_id: str) -> bool:
        """
        Set the default agent.
        
        Args:
            agent_id: The agent ID to set as default
            
        Returns:
            bool: True if successful, False if agent not found
        """
        agent = await AgentService.get_agent_by_id(agent_id)
        if agent:
            await AgentService.set_default_agent(agent_id)
            return True
        return False

    async def list_agents(self) -> List[Dict[str, Any]]:
        """
        List all agents and their information from the database.

        Returns:
            List[Dict[str, Any]]: List of agent information
        """
        agents = await AgentService.list_agents()
        return [agent.to_dict() for agent in agents]

    async def remove_agent(self, agent_id: str) -> bool:
        """
        Remove an agent from the database.
        
        Args:
            agent_id: The agent ID to remove
            
        Returns:
            bool: True if agent was removed, False if not found
        """
        try:
            await AgentService.delete_agent(agent_id)
            self.logger.info(f"Agent '{agent_id}' removed from the database.")
            return True
        except Exception as e:
            self.logger.error(f"Failed to remove agent '{agent_id}' from database.", exception=e)
            return False

    async def update_agent(
        self,
        agent_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        system_prompt: Optional[str] = None,
        set_as_default: Optional[bool] = None,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        llm_config: Optional[LLMConfig] = None,
        mcp_config: Optional[List[MCPServerConfig]] = None,
        structured_output_config: Optional[StructuredOutputConfig] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing agent in the database.
        
        Args:
            agent_id: The agent ID to update
            name: New name for the agent
            description: New description for the agent
            system_prompt: New system prompt for the agent
            set_as_default: Whether to set this agent as default
            provider: New LLM provider
            model: New model name
            llm_config: New LLM configuration
            mcp_config: New MCP server configuration list
            structured_output_config: New structured output configuration
            
        Returns:
            Optional[Dict[str, Any]]: Updated agent information, or None if not found
        """
        try:
            # Build updates dictionary with only provided values
            updates = {}
            
            if name is not None:
                updates['name'] = name
            if description is not None:
                updates['description'] = description
            if system_prompt is not None:
                updates['system_prompt'] = system_prompt
            if set_as_default is not None:
                updates['is_default'] = set_as_default
            if provider is not None:
                updates['provider'] = provider
            if model is not None:
                updates['model'] = model
            if llm_config is not None:
                updates['llm_config'] = llm_config.model_dump(exclude_none=True)
            if mcp_config is not None:
                updates['mcp_config'] = [config.model_dump() for config in mcp_config]
                # Sync MCP tools when MCP config is updated
                # Get all tools including MCP tools
                all_tools = self._global_tools.copy()
                
                # Add tools from specific MCP servers
                for mcp_server_config in mcp_config:
                    server_id = mcp_server_config.server_id
                    if server_id:
                        server_tools = self._mcp_manager.get_server_tools(server_id)
                        all_tools.extend(server_tools)
                        self.logger.debug(f"Added {len(server_tools)} tools from MCP server '{server_id}' for agent '{agent_id}'")
                
                # Update the tools field with the new tool names
                tool_names = [tool.metadata.name for tool in all_tools if hasattr(tool, 'metadata') and tool.metadata.name]
                updates['tools'] = tool_names
            
            if structured_output_config is not None:
                # Validate and process structured output configuration
                if structured_output_config.enabled and structured_output_config.schema_definition:
                    try:
                        validation_errors = self._model_generator.validate_schema(structured_output_config.schema_definition)
                        if validation_errors:
                            self.logger.warning(f"Structured output schema validation failed for agent '{agent_id}': {validation_errors}")
                            structured_output_config.enabled = False
                        else:
                            dynamic_model = self._model_generator.generate_model(structured_output_config)
                            if dynamic_model:
                                self.logger.info(f"✅ Successfully validated structured output config for agent '{agent_id}'")
                            else:
                                self.logger.warning(f"Failed to generate dynamic model for agent '{agent_id}'")
                                structured_output_config.enabled = False
                    except Exception as e:
                        self.logger.error(f"Error processing structured output configuration: {e}")
                        structured_output_config.enabled = False
                
                updates['structured_output_config'] = structured_output_config.model_dump(exclude_none=True)
            
            if not updates:
                self.logger.warning(f"No updates provided for agent '{agent_id}'")
                return None
            
            # Update the agent in the database
            updated_agent = await AgentService.update_agent(agent_id, **updates)
            
            if updated_agent:
                self.logger.info(f"Agent '{agent_id}' updated successfully in database")
                return updated_agent.to_dict()
            else:
                self.logger.warning(f"Agent '{agent_id}' not found for update")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to update agent '{agent_id}'", exception=e)
            raise

    async def stream_chat_with_agent(
        self,
        message: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        user_name: Optional[str] = None,
        user_id: Optional[str] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ):
        """
        Send a message to an agent and get a streaming response.

        Note: Workflow agents don't support native streaming.
        This method simulates streaming by yielding the full response.

        Args:
            message: The message to send
            agent_id: The agent ID (uses default if not provided)
            session_id: Session ID for chat history (optional, will generate if not provided)
            user_name: The user name to associate with the chat history
            user_id: The user ID to associate with the chat history
            attachments: List of attachment information dictionaries

        Yields:
            Dict or Event: Session info first (if generated), then chunks of the agent's response

        Raises:
            ValueError: If no agent is found
        """
        try:
            # Generate session_id if not provided
            chat_begin = False
            if not session_id:
                session_id = str(uuid.uuid4())
                # Yield session_id first before starting the conversation
                yield {"type": "session_id", "session_id": session_id}
                chat_begin = True
                self.logger.debug(f"Generated new session_id: {session_id}")

            agent = await self.get_agent(agent_id) if agent_id else await self.get_default_agent()
            if not agent:
                error_msg = f"No agent found with ID '{agent_id}'" if agent_id else "No default agent set"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            ctx = Context(agent)
            
            # Get actual agent_id for database operations
            actual_agent_id = agent_id
            if not actual_agent_id:
                default_agent = await AgentService.get_default_agent()
                if default_agent:
                    actual_agent_id = str(default_agent.agent_id)
                else:
                    actual_agent_id = "default"

            # Load chat memory with history
            memory = await self._load_chat_memory(session_id, message_limit=6)

            try:
                # Create or get chat history
                chat_history = await ChatService.get_chat_history(session_id)
                if not chat_history:
                    await ChatService.create_chat_history(session_id, actual_agent_id, user_name=user_name, user_id=user_id)

                # Save user message with attachments
                await ChatService.add_message(session_id, "user", message, attachments=attachments)
            except Exception as e:
                self.logger.warning(f"Failed to save user message to database: {e}")
                                     
            enhanced_message = message
            if attachments:
                # 构建包含附件内容的增强消息
                attachment_texts = []
                for attachment in attachments:
                    attachment_text = f"\n\n[附件: {attachment['original_filename']}]\n{attachment['content']}"
                    attachment_texts.append(attachment_text[-30000:])
                
                enhanced_message = message + "".join(attachment_texts)   
            handler = agent.run(enhanced_message, memory=memory)
            usage_detail = []
            first_token_recorded = False
            start_time = datetime.now()
            time_taken_s = 0.0
            
            # assistant_message = ""
            async for ev in handler.stream_events():
                # AgentOutput, ToolCall
                if isinstance(ev, AgentInput):
                    first_token_recorded = False
                    start_time = datetime.now()
                    yield ev.model_dump()
                    continue
                # if isinstance(ev, ToolCallResult):
                #     yield ev.model_dump()
                #     yield f"\nCall {ev.tool_name} with {ev.tool_kwargs}\nReturned: {ev.tool_output}"
                if isinstance(ev, AgentStream):
                    # Record the time of the first token
                    if not first_token_recorded:
                        first_token_time = datetime.now()
                        time_taken_s = (first_token_time - start_time).total_seconds()
                        self.logger.info(f"First token time for AgentOutput: {time_taken_s:.3f} s，start_time: {start_time}，first_token_time:{first_token_time}")
                        first_token_recorded = True
                    yield ev.model_dump(exclude={'response'})
                    continue
                # if isinstance(ev, ToolCallResult):
                #     assistant_message = f"{assistant_message}\n{str(ev)}"
                if isinstance(ev, AgentOutput):
                    yield ev.model_dump()
                    if ev.raw:
                        token_usage = ev.raw['usage']
                        if token_usage:
                            usage_detail.append({
                                'first_token_time_s': round(time_taken_s, 3),
                                'completion_tokens': token_usage.get('completion_tokens'),
                                'prompt_tokens': token_usage.get('prompt_tokens')
                            })
                    continue
                    # try:
                    #     await ChatService.add_message(session_id, "assistant", str(ev))
                    # except Exception as e:
                    #     self.logger.warning(f"Failed to save assistant response to database: {e}")
                        
                yield ev.model_dump()
                #     yield f"{ev.delta}"   
            response = await handler
            yield {"type": "usage_detail", "usage_detail": usage_detail}
            try:
                current_chat_history = await memory.aget()
                last_chat_history = current_chat_history[-1]
                content = str(last_chat_history.content)
                if content.startswith("Thought: ```\nThought: "):
                    content = content.replace("Thought: ```\nThought: ", "Thought: ", 1).replace("Answer: Answer:", "Answer:", 1)
                await ChatService.add_message(session_id, last_chat_history.role, content, {"usage_detail": usage_detail})
            except Exception as e:
                self.logger.warning(f"Failed to save assistant response to database: {e}")
                
            # Generate title for new conversations
            if chat_begin:
                try:
                    title = await self.generate_title(session_id, actual_agent_id)
                    yield {"type": "generate_title", "title": title}
                except Exception as e:
                    self.logger.warning(f"Failed to generate title for new session {session_id}: {e}")

        except Exception as e:
            self.logger.error(f"Failed to chat with agent {agent_id or 'default'}", exception=e)
            raise

    async def generate_title(self, session_id: str, agent_id: Optional[str] = None) -> str:
        """
        Generate a title for a given chat session.
        Args:
            session_id: The ID of the chat session.
            agent_id: The agent to use for title generation.
        Returns:
            The generated title.
        Raises:
            ValueError: If the session or agent is not found, or history is empty.
        """
        self.logger.info(f"Generating title for session_id: {session_id}")
        
        agent = await self.get_agent(agent_id) if agent_id else await self.get_default_agent()
        if not agent:
            error_msg = f"No agent found with ID '{agent_id}'" if agent_id else "No default agent set"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        messages = await ChatService.get_messages(session_id, limit=6)
        if not messages:
            error_msg = f"No chat history found for session {session_id} to generate a title."
            self.logger.warning(error_msg)
            raise ValueError(error_msg)

        conversation_text = "\n".join(
            [f"{msg.role}: {msg.content}" for msg in messages if msg.content and msg.content.strip()]
        )
        
        if not conversation_text:
            error_msg = "Chat history contains only empty messages."
            self.logger.warning(error_msg)
            raise ValueError(error_msg)

        title_prompt_str = (
            "Based on the following conversation, please generate a short, concise title "
            "(4-5 words) that summarizes the main topic. The title should be in the same language as the conversation.\n"
            "Do not use any quotation marks in the title.\n\n"
            "Conversation:\n"
            "---------------------\n"
            "{conversation_text}\n"
            "---------------------\n"
            "Title:"
        )
        title_prompt = PromptTemplate(title_prompt_str)

        try:
            with PerformanceLogger(f"generate_title_{session_id}", self.logger):
                response = await agent.llm.apredict(prompt=title_prompt, conversation_text=conversation_text)
                title = response.strip().strip('"')
        except Exception as e:
            self.logger.error(f"LLM failed to generate title for session {session_id}", exception=e)
            raise Exception("Failed to generate title from LLM.")

        if not title:
            self.logger.warning(f"LLM returned an empty title for session {session_id}")
            raise Exception("Generated title is empty.")

        try:
            await ChatService.update_chat_history(session_id, {"title": title})
            self.logger.info(f"Successfully generated and saved title for session {session_id}: '{title}'")
        except Exception as e:
            self.logger.error(f"Failed to save generated title for session {session_id}", exception=e)
            
        return title

    async def reset_agent(self, agent_id: Optional[str] = None) -> bool:
        """
        Reset an agent's conversation history.

        Note: Workflow agents don't maintain persistent state between runs.
        This method is provided for API compatibility but doesn't perform any action.

        Args:
            agent_id: The agent ID (uses default if not provided)

        Returns:
            bool: True if agent exists, False if agent not found
        """
        agent = await self.get_agent(agent_id) if agent_id else await self.get_default_agent()
        if agent:
            self.logger.info(f"🔄 Reset requested for agent '{agent_id or 'default'}' (workflow agents are stateless)")
            return True
        return False

    async def reset_all_agents(self) -> None:
        """
        Reset all agents' conversation histories.

        Note: Workflow agents don't maintain persistent state between runs.
        This method is provided for API compatibility but doesn't perform any action.
        """
        self.logger.info(f"🔄 Reset requested for all agents (workflow agents are stateless)")
        agents = await self.list_agents()
        for agent_info in agents:
            self.logger.debug(f"  - Agent '{agent_info.get('agent_id')}' (no action needed)")

    async def _load_chat_memory(self, session_id: str, message_limit: int = 6) -> Memory:
        """
        Format database chat messages for agent context.
        
        Args:
            messages: List of ChatMessage objects from database
            
        Returns:
            List of formatted message dictionaries with role and content
        """
        memory = Memory.from_defaults(session_id=session_id)
        
        try:
            formatted_messages = []
            
            # Load chat messages from database using ChatService.get_messages()
            messages = await ChatService.get_messages(session_id, limit=message_limit)
            
            if not messages:
                self.logger.debug(f"No chat history found for session {session_id}")
                return memory
            
            for message in messages:
                # Handle edge cases like empty messages or missing fields
                if not message:
                    self.logger.warning(f"Skipping malformed message: {message}")
                    continue
                
                if not hasattr(message, 'role') or not hasattr(message, 'content'):
                    self.logger.warning(f"Skipping malformed message: {message}")
                    continue
                
                # Skip messages with empty content
                if not message.content or not str(message.content).strip():
                    self.logger.debug(f"Skipping empty message with role: {message.role}")
                    continue
                
                formatted_message = ChatMessage(**message.to_dict())
                if message.attachments:
                    # 构建包含附件内容的增强消息
                    attachment_texts = []
                    for attachment in message.attachments:
                        attachment_text = f"\n\n[附件: {attachment['original_filename']}]\n{attachment['content']}"
                        attachment_texts.append(attachment_text[-30000:])
                
                    formatted_message.content = message.content + "".join(attachment_texts)
                
                # Validate role is one of expected values
                if formatted_message.role not in ["user", "assistant", "system"]:
                    self.logger.warning(f"Unknown message role '{formatted_message.role}', defaulting to 'user'")
                
                formatted_messages.append(formatted_message)
            
            self.logger.debug(f"Formatted {len(formatted_messages)} messages from {len(messages)} raw messages")
            memory.put_messages(formatted_messages)
            return memory
            
        except Exception as e:
            self.logger.error(f"Failed to format chat history: {e}")
            return memory

    def get_mcp_manager(self):
        """Get the MCP manager instance."""
        return self._mcp_manager

    async def get_mcp_tools(self, server_id: Optional[str] = None) -> List[BaseTool]:
        """
        Get MCP tools from a specific server or all servers.

        Args:
            server_id: Optional server ID to get tools from specific server

        Returns:
            List[BaseTool]: List of MCP tools
        """
        if server_id:
            return self._mcp_manager.get_server_tools(server_id)
        else:
            return self._mcp_manager.get_all_tools()

    def list_mcp_servers(self) -> Dict[str, Any]:
        """
        List all MCP servers and their status.

        Returns:
            Dict[str, Any]: Dictionary of MCP server statuses
        """
        return self._mcp_manager.list_servers()

    async def refresh_agent_tools(self, agent_id: Optional[str] = None) -> bool:
        """
        Refresh tools for an agent (recreate agent with updated tools).

        Note: This is needed because LlamaIndex agents don't support dynamic tool updates.

        Args:
            agent_id: Agent ID to refresh, or None to refresh all agents

        Returns:
            bool: True if successful
        """
        try:
            if agent_id:
                # To refresh an agent, we can simply re-fetch it, as it's always built from the DB.
                # However, the core issue is updating the tool list in the agent definition in the DB.
                # This method should probably update the tool list in the DB and then the agent
                # will be recreated with new tools on the next `get_agent` call.
                
                db_agent = await AgentService.get_agent_by_id(agent_id)
                if not db_agent:
                    self.logger.warning(f"Agent '{agent_id}' not found for tool refresh")
                    return False

                # Re-gather all tools
                all_tools = self._global_tools.copy()
                mcp_tools = self._mcp_manager.get_all_tools()
                all_tools.extend(mcp_tools)
                tool_names = [tool.metadata.name for tool in all_tools if hasattr(tool, 'metadata') and tool.metadata.name]

                # Update agent in the database with the new tool list
                await AgentService.update_agent(agent_id, **{"tools": tool_names})
                self.logger.info(f"Tools for agent '{agent_id}' have been refreshed in the database.")
                return True
            else:
                # TODO: Implement refreshing all agents
                self.logger.warning("Refreshing all agents is not yet implemented.")
                return False

        except Exception as e:
            self.logger.error(f"Failed to refresh agent tools for '{agent_id or 'all'}'", exception=e)
            return False


# Global agent manager instance
agent_manager = AgentManager()

"""
结构化输出验证器和错误处理模块。

此模块提供了结构化输出的验证、错误处理和回退策略。
"""

import json
from typing import Dict, Any, List, Optional, Type, Union
from pydantic import BaseModel, ValidationError
from enum import Enum

from app.agents.structured_output_models import (
    StructuredOutputConfig, 
    SchemaDefinition, 
    FieldDefinition,
    ParsingMode,
    ValidationLevel
)
from app.agents.dynamic_model_generator import DynamicModelGenerator


class ValidationErrorType(str, Enum):
    """验证错误类型枚举"""
    SCHEMA_INVALID = "schema_invalid"
    MODEL_GENERATION_FAILED = "model_generation_failed"
    OUTPUT_VALIDATION_FAILED = "output_validation_failed"
    PARSING_ERROR = "parsing_error"
    CONFIGURATION_ERROR = "configuration_error"


class StructuredOutputError(Exception):
    """结构化输出错误基类"""
    
    def __init__(self, error_type: ValidationErrorType, message: str, details: Optional[Dict[str, Any]] = None):
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(message)


class StructuredOutputValidator:
    """结构化输出验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self._model_generator = DynamicModelGenerator()
    
    def validate_configuration(self, config: StructuredOutputConfig) -> List[StructuredOutputError]:
        """
        验证结构化输出配置
        
        Args:
            config: 结构化输出配置
            
        Returns:
            验证错误列表，如果为空则表示验证通过
        """
        errors = []
        
        if not config.enabled:
            return errors
        
        # 验证模式定义
        if not config.schema_definition:
            errors.append(StructuredOutputError(
                ValidationErrorType.CONFIGURATION_ERROR,
                "启用结构化输出时必须提供模式定义"
            ))
            return errors
        
        # 验证模式定义的完整性
        schema_errors = self._model_generator.validate_schema(config.schema_definition)
        for schema_error in schema_errors:
            errors.append(StructuredOutputError(
                ValidationErrorType.SCHEMA_INVALID,
                f"模式定义错误: {schema_error}"
            ))
        
        # 验证解析模式
        if config.parsing_mode == ParsingMode.CUSTOM and not config.custom_parsing_code:
            errors.append(StructuredOutputError(
                ValidationErrorType.CONFIGURATION_ERROR,
                "自定义解析模式下必须提供解析代码"
            ))
        
        # 验证错误处理配置
        error_handling = config.error_handling or {}
        if not isinstance(error_handling, dict):
            errors.append(StructuredOutputError(
                ValidationErrorType.CONFIGURATION_ERROR,
                "错误处理配置必须是字典类型"
            ))
        else:
            # 验证重试次数
            max_retries = error_handling.get("max_retry_attempts", 3)
            if not isinstance(max_retries, int) or max_retries < 0:
                errors.append(StructuredOutputError(
                    ValidationErrorType.CONFIGURATION_ERROR,
                    "最大重试次数必须是非负整数"
                ))
        
        return errors
    
    def validate_output(self, output: str, config: StructuredOutputConfig, dynamic_model: Optional[Type[BaseModel]] = None) -> Dict[str, Any]:
        """
        验证和解析结构化输出
        
        Args:
            output: LLM 输出的原始文本
            config: 结构化输出配置
            dynamic_model: 动态生成的 Pydantic 模型
            
        Returns:
            包含解析结果的字典：
            {
                "success": bool,
                "data": Optional[Dict[str, Any]],
                "errors": List[str],
                "warnings": List[str]
            }
        """
        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": []
        }
        
        if not config.enabled or not config.schema_definition:
            result["warnings"].append("结构化输出未启用，返回原始文本")
            result["data"] = {"response": output}
            result["success"] = True
            return result
        
        try:
            # 根据解析模式处理输出
            if config.parsing_mode == ParsingMode.DIRECT:
                parsed_result = self._parse_direct(output, dynamic_model)
            elif config.parsing_mode == ParsingMode.STRUCTURED_LLM:
                parsed_result = self._parse_structured_llm(output, dynamic_model)
            elif config.parsing_mode == ParsingMode.CUSTOM:
                parsed_result = self._parse_custom(output, config.custom_parsing_code, dynamic_model)
            else:
                raise StructuredOutputError(
                    ValidationErrorType.CONFIGURATION_ERROR,
                    f"不支持的解析模式: {config.parsing_mode}"
                )
            
            result["data"] = parsed_result
            result["success"] = True
            
        except StructuredOutputError as e:
            result["errors"].append(f"结构化输出错误: {e.message}")
            if e.details:
                result["errors"].append(f"错误详情: {json.dumps(e.details, ensure_ascii=False)}")
        except Exception as e:
            result["errors"].append(f"解析输出时发生错误: {str(e)}")
        
        # 如果解析失败，应用回退策略
        if not result["success"] and config.error_handling.get("fallback_to_text", True):
            result["warnings"].append("结构化输出解析失败，回退到原始文本")
            result["data"] = {"response": output}
            result["success"] = True
        
        return result
    
    def _parse_direct(self, output: str, dynamic_model: Optional[Type[BaseModel]]) -> Dict[str, Any]:
        """
        直接解析模式
        
        Args:
            output: 原始输出文本
            dynamic_model: 动态模型
            
        Returns:
            解析后的数据
        """
        if not dynamic_model:
            raise StructuredOutputError(
                ValidationErrorType.MODEL_GENERATION_FAILED,
                "直接解析模式需要有效的动态模型"
            )
        
        try:
            # 尝试解析 JSON
            data = json.loads(output.strip())
            
            # 验证数据结构
            validated_data = dynamic_model(**data)
            return validated_data.model_dump()
            
        except json.JSONDecodeError:
            raise StructuredOutputError(
                ValidationErrorType.PARSING_ERROR,
                "输出不是有效的 JSON 格式"
            )
        except ValidationError as e:
            raise StructuredOutputError(
                ValidationErrorType.OUTPUT_VALIDATION_FAILED,
                "输出验证失败",
                {"validation_errors": e.errors()}
            )
    
    def _parse_structured_llm(self, output: str, dynamic_model: Optional[Type[BaseModel]]) -> Dict[str, Any]:
        """
        结构化 LLM 解析模式
        
        Args:
            output: 原始输出文本
            dynamic_model: 动态模型
            
        Returns:
            解析后的数据
        """
        # 这里模拟结构化 LLM 的解析过程
        # 实际实现可能需要使用特定的 LLM 结构化输出功能
        
        # 尝试从输出中提取 JSON 部分
        import re
        json_pattern = r'\\{[^}]*\\}'  # 简化的 JSON 匹配模式
        json_matches = re.findall(json_pattern, output)
        
        if not json_matches:
            raise StructuredOutputError(
                ValidationErrorType.PARSING_ERROR,
                "输出中未找到 JSON 结构"
            )
        
        # 尝试解析找到的 JSON
        for json_str in json_matches:
            try:
                # 尝试补全 JSON 字符串
                full_json = json_str
                if not json_str.startswith('{'):
                    full_json = '{' + json_str
                if not json_str.endswith('}'):
                    full_json = full_json + '}'
                
                data = json.loads(full_json)
                
                if dynamic_model:
                    validated_data = dynamic_model(**data)
                    return validated_data.model_dump()
                else:
                    return data
                    
            except (json.JSONDecodeError, ValidationError):
                continue
        
        raise StructuredOutputError(
            ValidationErrorType.PARSING_ERROR,
            "无法解析输出中的 JSON 结构"
        )
    
    def _parse_custom(self, output: str, custom_code: str, dynamic_model: Optional[Type[BaseModel]]) -> Dict[str, Any]:
        """
        自定义解析模式
        
        Args:
            output: 原始输出文本
            custom_code: 自定义解析代码
            dynamic_model: 动态模型
            
        Returns:
            解析后的数据
        """
        if not custom_code:
            raise StructuredOutputError(
                ValidationErrorType.CONFIGURATION_ERROR,
                "自定义解析代码不能为空"
            )
        
        try:
            # 在受限环境中执行自定义代码
            # 注意：实际生产环境中需要更严格的安全措施
            exec_globals = {
                "output": output,
                "json": json,
                "dynamic_model": dynamic_model,
                "StructuredOutputError": StructuredOutputError
            }
            exec_locals = {}
            
            exec(custom_code, exec_globals, exec_locals)
            
            # 检查是否返回了解析结果
            if "parsed_result" not in exec_locals:
                raise StructuredOutputError(
                    ValidationErrorType.PARSING_ERROR,
                    "自定义解析代码必须定义 'parsed_result' 变量"
                )
            
            parsed_result = exec_locals["parsed_result"]
            
            # 如果有动态模型，验证结果
            if dynamic_model and isinstance(parsed_result, dict):
                validated_data = dynamic_model(**parsed_result)
                return validated_data.model_dump()
            
            return parsed_result
            
        except Exception as e:
            raise StructuredOutputError(
                ValidationErrorType.PARSING_ERROR,
                f"自定义解析代码执行失败: {str(e)}"
            )


class StructuredOutputHandler:
    """结构化输出处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self._validator = StructuredOutputValidator()
    
    async def process_output(self, output: str, config: StructuredOutputConfig, dynamic_model: Optional[Type[BaseModel]] = None) -> Dict[str, Any]:
        """
        处理结构化输出
        
        Args:
            output: LLM 输出的原始文本
            config: 结构化输出配置
            dynamic_model: 动态生成的 Pydantic 模型
            
        Returns:
            处理结果字典
        """
        # 验证配置
        config_errors = self._validator.validate_configuration(config)
        if config_errors:
            return {
                "success": False,
                "data": {"response": output},
                "errors": [f"配置验证失败: {error.message}" for error in config_errors],
                "warnings": ["配置无效，回退到原始文本"]
            }
        
        # 验证和解析输出
        result = self._validator.validate_output(output, config, dynamic_model)
        
        return result
    
    def get_fallback_response(self, original_output: str, errors: List[str]) -> Dict[str, Any]:
        """
        获取回退响应
        
        Args:
            original_output: 原始输出文本
            errors: 错误列表
            
        Returns:
            回退响应字典
        """
        return {
            "success": True,
            "data": {
                "response": original_output,
                "fallback_reason": "结构化输出处理失败",
                "errors": errors
            },
            "warnings": ["使用回退响应"],
            "errors": []
        }


# 全局实例
structured_output_validator = StructuredOutputValidator()
structured_output_handler = StructuredOutputHandler()
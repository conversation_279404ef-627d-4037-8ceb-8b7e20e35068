"""
Easy Agent Center - LlamaIndex Agents Module

This module provides a comprehensive agent system built on top of LlamaIndex,
offering various types of agents for different use cases.
"""

from .agent_manager import AgentManager
from ..logger.logger import get_logger, setup_logging, PerformanceLogger, log_exception
from ..routers.agent import router as agent_router
from ..routers.agent_chat import router as chat_router
from ..routers.agent_manager_chat import router as manager_chat_router
from .models import (
    CreateAgentRequest,
    UpdateAgentRequest,
    AgentResponse,
    AgentSafeResponse,
    AgentDeleteResponse,
    AgentSetDefaultResponse,
    ChatRequest,
    ChatResponse,
    ChatHistoryResponse,
    ChatMessageResponse
)

__all__ = [
    "AgentManager",
    "get_logger",
    "setup_logging",
    "PerformanceLogger",
    "log_exception",
    "agent_router",
    "chat_router",
    "manager_chat_router",
    # Models
    "CreateAgentRequest",
    "UpdateAgentRequest",
    "AgentResponse",
    "AgentSafeResponse",
    "AgentDeleteResponse",
    "AgentSetDefaultResponse",
    "ChatRequest",
    "ChatResponse",
    "ChatHistoryResponse",
    "ChatMessageResponse"
]

__version__ = "0.1.0"

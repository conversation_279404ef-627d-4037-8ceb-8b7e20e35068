"""
Pydantic models for Agent API endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from app.mcp.models import MCPServerConfig
from app.schemas.common import PaginatedResponse
from app.agents.structured_output_models import StructuredOutputConfig

# Agent-related models
class LLMConfig(BaseModel):
    """LLM 配置"""
    provider_id: str = Field(..., description="供应程序的唯一标识符。")
    name: str = Field(..., description="供应程序的人性化名称。")
    provider_type: str = Field(..., description="供应程序的类型。")
    api_base: Optional[str] = Field(None, description="API 的基础 URL。")
    model: str = Field(..., description="默认模型。")
    context_window: int = Field(..., description="上下文窗口大小。")
    max_tokens: Optional[int] = Field(None, description="最大令牌数。")
    temperature: float = Field(..., description="生成温度。")
    timeout: int = Field(..., description="API 请求超时时间。")
    max_retries: int = Field(..., description="API 请求最大重试次数。")
    is_chat_model: bool = Field(..., description="是否为聊天模型。")
    is_function_calling_model: bool = Field(..., description="是否支持函数调用。")
    extra_params: Optional[Dict[str, Any]] = Field(None, description="额外的 LLM 参数。")

class CreateAgentRequest(BaseModel):
    """用于创建新代理的请求模型。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    agent_type: str = Field("react", description="代理的类型（例如 'react'）。")
    name: Optional[str] = Field(None, description="代理的人性化名称。")
    description: Optional[str] = Field(None, description="代理的描述。")
    system_prompt: Optional[str] = Field(None, description="代理的系统提示。")
    set_as_default: bool = Field(False, description="是否将此代理设置为默认。")
    provider: str = Field(..., description="LLM provider")
    model: str = Field("gpt-3.5-turbo", description="要使用的模型名称。")
    llm_config: Optional[LLMConfig] = Field(None, description="LLM 的特定配置。")
    mcp_config: Optional[List[MCPServerConfig]] = Field(default=[], description="MCP 服务器配置列表。")
    structured_output_config: Optional[StructuredOutputConfig] = Field(None, description="结构化输出配置。")


class UpdateAgentRequest(BaseModel):
    """用于更新现有代理的请求模型。"""
    name: Optional[str] = Field(None, description="代理的新名称。")
    description: Optional[str] = Field(None, description="代理的新描述。")
    system_prompt: Optional[str] = Field(None, description="代理的新系统提示。")
    set_as_default: Optional[bool] = Field(None, description="是否将此代理设置为默认。")
    provider: Optional[str] = Field(None, description="LLM provider")
    model: Optional[str] = Field(None, description="新的模型名称。")
    llm_config: Optional[LLMConfig] = Field(None, description="新的LLM配置。")
    mcp_config: Optional[List[MCPServerConfig]] = Field(None, description="新的MCP服务器配置列表。")
    structured_output_config: Optional[StructuredOutputConfig] = Field(None, description="结构化输出配置。")


class AgentResponse(BaseModel):
    """代理信息的响应模型。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    name: str = Field(..., description="代理的名称。")
    description: Optional[str] = Field(..., description="代理的描述。")
    agent_type: str = Field(..., description="代理的类型。")
    system_prompt: Optional[str] = Field(..., description="代理的系统提示。")
    model: str = Field(..., description="代理使用的模型。")
    provider: str = Field(..., description="LLM provider")
    tools: List[str] = Field(..., description="代理使用的工具列表。")
    is_default: bool = Field(..., description="此代理是否为默认代理。")
    is_active: bool = Field(..., description="此代理是否处于活动状态。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")
    llm_config: Optional[LLMConfig] = Field(None, description="LLM 配置信息。")
    mcp_config: Optional[List[MCPServerConfig]] = Field(None, description="MCP 服务器配置列表。")
    structured_output_config: Optional[StructuredOutputConfig] = Field(None, description="结构化输出配置。")


class AgentSafeResponse(BaseModel):
    """代理信息的安全响应模型（不含敏感数据）。"""
    agent_id: str = Field(..., description="代理的唯一标识符。")
    name: str = Field(..., description="代理的名称。")
    description: Optional[str] = Field(..., description="代理的描述。")
    agent_type: str = Field(..., description="代理的类型。")
    system_prompt: Optional[str] = Field(..., description="代理的系统提示。")
    model: str = Field(..., description="代理使用的模型。")
    provider: str = Field(..., description="LLM provider")
    tools: List[str] = Field(..., description="代理使用的工具列表。")
    is_default: bool = Field(..., description="此代理是否为默认代理。")
    is_active: bool = Field(..., description="此代理是否处于活动状态。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")
    llm_config: Optional[LLMConfig] = Field(None, description="LLM 配置信息。")
    mcp_config: Optional[List[MCPServerConfig]] = Field(None, description="MCP 服务器配置列表。")
    structured_output_config: Optional[StructuredOutputConfig] = Field(None, description="结构化输出配置。")
    agent_config: Dict[str, Any] | None = Field(None, description="智能体的扩展配置")


class AgentDeleteResponse(BaseModel):
    """代理删除的响应模型。"""
    message: str = Field(..., description="删除操作的结果消息。")


class AgentSetDefaultResponse(BaseModel):
    """设置默认代理的响应模型。"""
    message: str = Field(..., description="设置默认操作的结果消息。")


class ChatRequest(BaseModel):
    """与代理聊天的请求模型。"""
    message: str = Field(..., description="要发送给代理的消息。")
    agent_id: Optional[str] = Field(None, description="要聊天的代理的 ID（如果未提供，则使用默认代理）。")
    session_id: Optional[str] = Field(None, description="用于维护对话历史记录的会话 ID。")
    attachment_ids: Optional[List[int]] = Field(default=[], description="附件文件记录ID列表。")


class ChatResponse(BaseModel):
    """与代理聊天的响应模型。"""
    response: str = Field(..., description="来自代理的响应消息。")
    agent_id: str = Field(..., description="响应该聊天的代理的 ID。")
    agent_name: str = Field(..., description="响应该聊天的代理的名称。")


class ChatHistoryResponse(BaseModel):
    """聊天历史的响应模型。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    session_id: str = Field(..., description="会话的唯一标识符。")
    agent_id: str = Field(..., description="与此聊天历史关联的代理的 ID。")
    title: Optional[str] = Field(..., description="聊天的标题。")
    user_name: str = Field(..., description="用户名称。")
    user_id: Optional[str] = Field(None, description="用户 ID。")
    created_at: str = Field(..., description="创建时间戳。")
    updated_at: str = Field(..., description="最后更新时间戳。")




class ChatMessageResponse(BaseModel):
    """聊天消息的响应模型。"""
    id: int = Field(..., description="数据库中的唯一 ID。")
    role: str = Field(..., description="消息的角色（例如 'user', 'assistant'）。")
    content: str = Field(..., description="消息的内容。")
    created_at: str = Field(..., description="创建时间戳。")
    metadata: Optional[Dict[str, Any]] = Field(None, description="与消息关联的元数据。")
    attachments: Optional[List[Dict[str, Any]]] = Field(default=[], description="附件信息列表。")


class GenerateTitleRequest(BaseModel):
    """生成标题的请求模型。"""
    session_id: str = Field(..., description="需要生成标题的会话 ID。")
    agent_id: Optional[str] = Field(None, description="用于生成标题的代理 ID（如果未提供，则使用默认代理）。")


"""
结构化输出配置的数据模型。

此模块定义了用于配置Agent结构化输出的Pydantic模型，
包括模式定义、字段配置和验证规则。
"""

from typing import Dict, Any, List, Optional, Union, Literal
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class FieldType(str, Enum):
    """支持的字段类型枚举。"""
    STR = "str"
    INT = "int"
    FLOAT = "float"
    BOOL = "bool"
    LIST = "list"
    DICT = "dict"
    DATETIME = "datetime"
    JSON = "json"


class ParsingMode(str, Enum):
    """结构化输出解析模式。"""
    DIRECT = "direct"  # 直接使用output_cls
    STRUCTURED_LLM = "structured_llm"  # 使用as_structured_llm
    CUSTOM = "custom"  # 自定义解析函数


class ValidationLevel(str, Enum):
    """验证级别。"""
    STRICT = "strict"  # 严格验证
    LENIENT = "lenient"  # 宽松验证


class FieldDefinition(BaseModel):
    """Pydantic模式字段定义。"""
    
    name: str = Field(..., description="字段名称", min_length=1, max_length=100)
    type: FieldType = Field(..., description="字段类型")
    description: str = Field(..., description="字段描述", min_length=1)
    required: bool = Field(True, description="是否必需字段")
    default: Optional[Any] = Field(None, description="默认值")
    examples: Optional[List[Any]] = Field(None, description="示例值")
    constraints: Optional[Dict[str, Any]] = Field(None, description="字段约束（如最小值、最大值等）")
    
    @field_validator('constraints')
    @classmethod
    def validate_constraints(cls, v):
        """验证字段约束的合理性。"""
        if v is not None:
            # 可以添加更详细的约束验证逻辑
            pass
        return v


class SchemaDefinition(BaseModel):
    """Pydantic模式定义。"""
    
    class_name: str = Field(..., description="生成的Pydantic类名", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="模式描述")
    fields: List[FieldDefinition] = Field(..., description="字段定义列表", min_length=1)
    base_classes: Optional[List[str]] = Field(None, description="基类名称列表")
    imports: Optional[List[str]] = Field(None, description="需要导入的模块")
    extra_code: Optional[str] = Field(None, description="额外的代码片段")
    
    @field_validator('class_name')
    @classmethod
    def validate_class_name(cls, v):
        """验证类名的有效性。"""
        if not v.isidentifier():
            raise ValueError("类名必须是有效的Python标识符")
        if v[0].islower():
            raise ValueError("类名必须以大写字母开头")
        return v
    
    @field_validator('fields')
    @classmethod
    def validate_field_names(cls, v):
        """验证字段名的唯一性。"""
        names = [field.name for field in v]
        if len(names) != len(set(names)):
            raise ValueError("字段名必须唯一")
        return v


class StructuredOutputConfig(BaseModel):
    """结构化输出配置。"""
    
    enabled: bool = Field(False, description="是否启用结构化输出")
    schema_type: Literal["pydantic"] = Field("pydantic", description="模式类型")
    schema_definition: Optional[SchemaDefinition] = Field(None, description="模式定义")
    parsing_mode: ParsingMode = Field(ParsingMode.DIRECT, description="解析模式")
    validation_level: ValidationLevel = Field(ValidationLevel.STRICT, description="验证级别")
    custom_parsing_code: Optional[str] = Field(None, description="自定义解析代码")
    error_handling: Dict[str, Any] = Field(
        default_factory=lambda: {
            "fallback_to_text": True,
            "include_error_details": False,
            "max_retry_attempts": 3
        },
        description="错误处理配置"
    )
    
    @field_validator('schema_definition')
    @classmethod
    def validate_schema_definition(cls, v, info):
        """验证模式定义的完整性。"""
        if info.data.get('enabled') and v is None:
            raise ValueError("启用结构化输出时必须提供模式定义")
        return v
    
    @field_validator('custom_parsing_code')
    @classmethod
    def validate_custom_parsing_code(cls, v, info):
        """验证自定义解析代码。"""
        if info.data.get('parsing_mode') == ParsingMode.CUSTOM and not v:
            raise ValueError("自定义解析模式下必须提供解析代码")
        return v


class StructuredOutputTemplate(BaseModel):
    """预定义的结构化输出模板。"""
    
    template_id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    category: str = Field(..., description="模板分类")
    schema_definition: SchemaDefinition = Field(..., description="模式定义")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    is_builtin: bool = Field(False, description="是否为内置模板")
    usage_count: int = Field(0, description="使用次数")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class StructuredOutputValidationResult(BaseModel):
    """结构化输出验证结果。"""
    
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    generated_class: Optional[str] = Field(None, description="生成的类名")
    validation_details: Dict[str, Any] = Field(default_factory=dict, description="验证详情")


class StructuredOutputExample(BaseModel):
    """结构化输出示例。"""
    
    template_id: str = Field(..., description="模板ID")
    input_text: str = Field(..., description="输入文本")
    expected_output: Dict[str, Any] = Field(..., description="期望输出")
    description: str = Field(..., description="示例描述")
    difficulty_level: str = Field("beginner", description="难度级别")


# 常用的预定义模板
COMMON_TEMPLATES = {
    "sentiment_analysis": StructuredOutputTemplate(
        template_id="sentiment_analysis",
        name="情感分析",
        description="分析文本的情感倾向",
        category="文本分析",
        schema_definition=SchemaDefinition(
            class_name="SentimentAnalysis",
            description="文本情感分析结果",
            fields=[
                FieldDefinition(
                    name="sentiment",
                    type=FieldType.STR,
                    description="情感倾向（positive/negative/neutral）",
                    required=True,
                    examples=["positive", "negative", "neutral"]
                ),
                FieldDefinition(
                    name="confidence",
                    type=FieldType.FLOAT,
                    description="置信度分数（0-1）",
                    required=True,
                    constraints={"min": 0.0, "max": 1.0},
                    examples=[0.95, 0.8, 0.6]
                ),
                FieldDefinition(
                    name="key_phrases",
                    type=FieldType.LIST,
                    description="关键短语列表",
                    required=False,
                    examples=[["产品质量", "服务态度"], ["价格合理"]]
                )
            ]
        ),
        tags=["情感分析", "NLP", "文本处理"],
        is_builtin=True,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    ),
    
    "qa_response": StructuredOutputTemplate(
        template_id="qa_response",
        name="问答响应",
        description="结构化的问答响应",
        category="问答系统",
        schema_definition=SchemaDefinition(
            class_name="QAResponse",
            description="问答系统响应",
            fields=[
                FieldDefinition(
                    name="answer",
                    type=FieldType.STR,
                    description="答案内容",
                    required=True
                ),
                FieldDefinition(
                    name="confidence",
                    type=FieldType.FLOAT,
                    description="答案置信度",
                    required=True,
                    constraints={"min": 0.0, "max": 1.0}
                ),
                FieldDefinition(
                    name="sources",
                    type=FieldType.LIST,
                    description="来源信息",
                    required=False
                ),
                FieldDefinition(
                    name="follow_up_questions",
                    type=FieldType.LIST,
                    description="后续问题建议",
                    required=False
                )
            ]
        ),
        tags=["问答", "知识库", "搜索"],
        is_builtin=True,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    ),
    
    "data_extraction": StructuredOutputTemplate(
        template_id="data_extraction",
        name="数据提取",
        description="从文本中提取结构化数据",
        category="信息提取",
        schema_definition=SchemaDefinition(
            class_name="ExtractedData",
            description="从文本中提取的结构化数据",
            fields=[
                FieldDefinition(
                    name="entities",
                    type=FieldType.LIST,
                    description="提取的实体列表",
                    required=True
                ),
                FieldDefinition(
                    name="relationships",
                    type=FieldType.LIST,
                    description="实体关系列表",
                    required=False
                ),
                FieldDefinition(
                    name="confidence",
                    type=FieldType.FLOAT,
                    description="整体提取置信度",
                    required=True
                )
            ]
        ),
        tags=["信息提取", "实体识别", "NLP"],
        is_builtin=True,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    )
}


def get_template(template_id: str) -> Optional[StructuredOutputTemplate]:
    """获取预定义模板。"""
    return COMMON_TEMPLATES.get(template_id)


def list_templates() -> List[StructuredOutputTemplate]:
    """列出所有预定义模板。"""
    return list(COMMON_TEMPLATES.values())


def get_templates_by_category(category: str) -> List[StructuredOutputTemplate]:
    """按分类获取模板。"""
    return [template for template in COMMON_TEMPLATES.values() 
            if template.category == category]
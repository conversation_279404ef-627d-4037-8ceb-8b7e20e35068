"""add_file_upload_records_table

Revision ID: fd8035196953
Revises: f144e6b723ea
Create Date: 2025-08-15 11:49:55.701259

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fd8035196953'
down_revision = 'f144e6b723ea'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('file_upload_records',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('original_filename', sa.String(length=500), nullable=False, comment='原始文件名'),
    sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小（字节）'),
    sa.Column('content_type', sa.String(length=255), nullable=True, comment='文件MIME类型'),
    sa.Column('storage_type', sa.String(length=50), nullable=False, comment='存储类型 (tos, local)'),
    sa.Column('object_key', sa.String(length=1000), nullable=True, comment='对象存储中的键名'),
    sa.Column('file_url', sa.String(length=1000), nullable=True, comment='文件访问URL'),
    sa.Column('upload_status', sa.String(length=50), nullable=False, comment='上传状态 (pending, success, failed)'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('uploader_id', sa.String(length=255), nullable=True, comment='上传者ID'),
    sa.Column('uploader_name', sa.String(length=255), nullable=False, comment='上传者名称'),
    sa.Column('upload_metadata', sa.JSON(), nullable=True, comment='上传元数据 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='文件上传记录表'
    )
    op.create_index(op.f('ix_file_upload_records_uploader_id'), 'file_upload_records', ['uploader_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_file_upload_records_uploader_id'), table_name='file_upload_records')
    op.drop_table('file_upload_records')
    # ### end Alembic commands ###

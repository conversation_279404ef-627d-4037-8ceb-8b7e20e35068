"""chat_message_add_attach

Revision ID: 40c05d2b0f7b
Revises: 4377aeccdfee
Create Date: 2025-08-25 10:37:04.424965

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '40c05d2b0f7b'
down_revision = '4377aeccdfee'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('attachments', sa.JSON(), nullable=True, comment='附件信息 (JSON数组)，包含FileUploadRecord的id、original_filename、file_url、content_type'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_messages', 'attachments')
    # ### end Alembic commands ###

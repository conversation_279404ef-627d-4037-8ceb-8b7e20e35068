"""alter_agent_add_format

Revision ID: b206d4d05325
Revises: 90a0cf65f586
Create Date: 2025-08-27 14:58:21.538156

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b206d4d05325'
down_revision = '90a0cf65f586'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('structured_output_config', sa.JSON(), nullable=True, comment='结构化输出配置 (JSON)'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agents', 'structured_output_config')
    # ### end Alembic commands ###

"""modeify_history_u_id

Revision ID: 885f23e3f23d
Revises: a40aae1a46e5
Create Date: 2025-08-07 17:56:45.975450

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '885f23e3f23d'
down_revision = 'a40aae1a46e5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_histories', 'biz_u_id', new_column_name='user_id', type_=mysql.VARCHAR(255), nullable=True, comment='用户ID')
    # ### end Alembic commands ###


def downgrade() -> None:
    # Rename user_id column back to biz_u_id
    op.alter_column('chat_histories', 'user_id', new_column_name='biz_u_id', type_=mysql.VARCHAR(255), nullable=True, comment='业务用户id')

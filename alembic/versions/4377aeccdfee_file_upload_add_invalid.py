"""file_upload_add_invalid

Revision ID: 4377aeccdfee
Revises: ce29a0b603ab
Create Date: 2025-08-21 19:02:51.391273

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4377aeccdfee'
down_revision = 'ce29a0b603ab'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('file_upload_records', sa.Column('invalid', sa.Integer(), nullable=False, server_default='0', comment='删除标记：0 有效，1 已删除'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('file_upload_records', 'invalid')
    # ### end Alembic commands ###

"""create_smart_config

Revision ID: 90a0cf65f586
Revises: 40c05d2b0f7b
Create Date: 2025-08-26 14:12:35.436186

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '90a0cf65f586'
down_revision = '40c05d2b0f7b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('smart_config',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('config_key', sa.String(length=64), nullable=False, comment='配置键'),
    sa.Column('second_key', sa.String(length=64), nullable=False, comment='配置键'),
    sa.Column('config_value', sa.Text(), nullable=False, comment='配置值'),
    sa.Column('config_order', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('ext_info', sa.String(length=255), nullable=True, comment='扩展信息'),
    sa.Column('config_type', sa.Integer(), nullable=False, comment='配置类型 0 single, 1 chunk'),
    sa.Column('invalid', sa.Integer(), nullable=False, comment='删除标记：0 有效，1 已删除'),
    sa.Column('created_by', sa.Integer(), nullable=False, comment='创建人'),
    sa.Column('updated_by', sa.Integer(), nullable=False, comment='修改人'),
    sa.Column('gmt_create', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('gmt_modified', sa.DateTime(), nullable=False, comment='修改时间'),
    sa.Column('ts', sa.DateTime(), nullable=False, comment='修改时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='智能配置表'
    )
    op.create_index(op.f('ix_smart_config_config_key'), 'smart_config', ['config_key'], unique=False)
    op.alter_column('file_upload_records', 'upload_status',
               existing_type=mysql.VARCHAR(length=50),
               comment='上传状态 (pending, extracting, success, failed)',
               existing_comment='上传状态 (pending, success, failed)',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('file_upload_records', 'upload_status',
               existing_type=mysql.VARCHAR(length=50),
               comment='上传状态 (pending, success, failed)',
               existing_comment='上传状态 (pending, extracting, success, failed)',
               existing_nullable=False)
    op.drop_index(op.f('ix_smart_config_config_key'), table_name='smart_config')
    op.drop_table('smart_config')
    # ### end Alembic commands ###

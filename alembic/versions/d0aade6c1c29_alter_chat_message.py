"""alter_chat_message

Revision ID: d0aade6c1c29
Revises: 885f23e3f23d
Create Date: 2025-08-12 11:12:54.935403

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd0aade6c1c29'
down_revision = '885f23e3f23d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_messages', 'content',
               existing_type=mysql.TEXT(),
               type_=mysql.MEDIUMTEXT(),
               existing_comment='消息内容',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_messages', 'content',
               existing_type=mysql.MEDIUMTEXT(),
               type_=mysql.TEXT(),
               existing_comment='消息内容',
               existing_nullable=False)
    # ### end Alembic commands ###

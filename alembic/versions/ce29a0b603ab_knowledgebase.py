"""knowledgeBase

Revision ID: ce29a0b603ab
Revises: fd8035196953
Create Date: 2025-08-20 19:39:38.184116

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'ce29a0b603ab'
down_revision = 'fd8035196953'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('document_chunks', 'kb_id',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Integer(),
               existing_comment='所属知识库 ID',
               existing_nullable=False)
    op.alter_column('documents', 'kb_id',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.Integer(),
               existing_comment='所属知识库 ID',
               existing_nullable=False)
    op.add_column('knowledge_bases', sa.Column('relation_kb_id', sa.String(length=255), nullable=False, comment='火山引擎知识库ID，用于API集成'))
    op.add_column('knowledge_bases', sa.Column('invalid', sa.Integer(), nullable=False, comment='删除标记：0 有效，1 已删除'))
    op.add_column('knowledge_bases', sa.Column('volc_config', sa.JSON(), nullable=True, comment='火山引擎知识库配置，包含collection_name等信息'))
    op.drop_index(op.f('ix_knowledge_bases_kb_id'), table_name='knowledge_bases')
    op.create_index(op.f('ix_knowledge_bases_relation_kb_id'), 'knowledge_bases', ['relation_kb_id'], unique=False)
    op.drop_column('knowledge_bases', 'kb_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('knowledge_bases', sa.Column('kb_id', mysql.VARCHAR(length=255), nullable=False, comment='知识库的唯一标识符'))
    op.drop_index(op.f('ix_knowledge_bases_relation_kb_id'), table_name='knowledge_bases')
    op.create_index(op.f('ix_knowledge_bases_kb_id'), 'knowledge_bases', ['kb_id'], unique=True)
    op.drop_column('knowledge_bases', 'volc_config')
    op.drop_column('knowledge_bases', 'invalid')
    op.drop_column('knowledge_bases', 'relation_kb_id')
    op.alter_column('documents', 'kb_id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=255),
               existing_comment='所属知识库 ID',
               existing_nullable=False)
    op.alter_column('document_chunks', 'kb_id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=255),
               existing_comment='所属知识库 ID',
               existing_nullable=False)
    # ### end Alembic commands ###

"""alter_chat_message_add_usage

Revision ID: f144e6b723ea
Revises: d0aade6c1c29
Create Date: 2025-08-12 14:44:14.206228

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f144e6b723ea'
down_revision = 'd0aade6c1c29'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('completion_tokens', sa.Integer(), nullable=True, comment='完成令牌数'))
    op.add_column('chat_messages', sa.Column('prompt_tokens', sa.Integer(), nullable=True, comment='提示令牌数'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_messages', 'prompt_tokens')
    op.drop_column('chat_messages', 'completion_tokens')
    # ### end Alembic commands ###
